const { <PERSON><PERSON><PERSON>ommandBuilder, <PERSON><PERSON><PERSON><PERSON>er, ActionRowBuilder, ButtonBuilder, ButtonStyle, ModalBuilder, TextInputBuilder, TextInputStyle } = require('discord.js');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('coupon')
        .setDescription('🎫 استخدام كوبون خصم')
        .addStringOption(option =>
            option.setName('code')
                .setDescription('كود الكوبون')
                .setRequired(true)),
    
    async execute(interaction, bot) {
        try {
            const couponCode = interaction.options.getString('code').toUpperCase();
            
            // البحث عن الكوبون
            const coupon = await bot.database.get(
                'SELECT * FROM coupons WHERE code = ? AND is_active = 1',
                [couponCode]
            );
            
            if (!coupon) {
                const errorEmbed = new EmbedBuilder()
                    .setTitle('❌ كوبون غير صالح')
                    .setDescription('الكوبون المدخل غير موجود أو منتهي الصلاحية.')
                    .setColor('#FF0000')
                    .addFields(
                        { name: '💡 نصائح', value: '• تأكد من كتابة الكود بشكل صحيح\n• تحقق من تاريخ انتهاء الصلاحية\n• تأكد من عدم استخدام الكود من قبل', inline: false }
                    );
                
                return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }
            
            // التحقق من انتهاء الصلاحية
            if (coupon.expires_at && new Date() > new Date(coupon.expires_at)) {
                const expiredEmbed = new EmbedBuilder()
                    .setTitle('⏰ انتهت صلاحية الكوبون')
                    .setDescription(`انتهت صلاحية هذا الكوبون في ${new Date(coupon.expires_at).toLocaleDateString('ar-EG')}`)
                    .setColor('#FFA500');
                
                return await interaction.reply({ embeds: [expiredEmbed], ephemeral: true });
            }
            
            // التحقق من عدد الاستخدامات
            if (coupon.max_uses !== -1 && coupon.used_count >= coupon.max_uses) {
                const maxUsedEmbed = new EmbedBuilder()
                    .setTitle('🚫 تم استنفاد الكوبون')
                    .setDescription('تم استخدام هذا الكوبون للحد الأقصى المسموح به.')
                    .setColor('#FF0000');
                
                return await interaction.reply({ embeds: [maxUsedEmbed], ephemeral: true });
            }
            
            // التحقق من استخدام المستخدم للكوبون من قبل
            const userUsage = await bot.database.get(
                `SELECT * FROM coupon_usage 
                 WHERE coupon_id = ? AND user_id = (SELECT id FROM users WHERE discord_id = ?)`,
                [coupon.id, interaction.user.id]
            );
            
            if (userUsage) {
                const alreadyUsedEmbed = new EmbedBuilder()
                    .setTitle('🔄 تم استخدام الكوبون من قبل')
                    .setDescription('لقد استخدمت هذا الكوبون من قبل. كل كوبون يمكن استخدامه مرة واحدة فقط لكل مستخدم.')
                    .setColor('#FFA500');
                
                return await interaction.reply({ embeds: [alreadyUsedEmbed], ephemeral: true });
            }
            
            // حساب قيمة الخصم
            let discountText = '';
            if (coupon.discount_type === 'percentage') {
                discountText = `${coupon.discount_value}%`;
            } else {
                discountText = `${coupon.discount_value} كريدت`;
            }
            
            // عرض تفاصيل الكوبون
            const validEmbed = new EmbedBuilder()
                .setTitle('✅ كوبون صالح!')
                .setDescription(`
**تهانينا! الكوبون صالح للاستخدام**

🎫 **كود الكوبون:** \`${coupon.code}\`
💰 **قيمة الخصم:** ${discountText}
${coupon.min_amount > 0 ? `📊 **الحد الأدنى للطلب:** ${coupon.min_amount} كريدت` : ''}

**كيفية الاستخدام:**
سيتم تطبيق هذا الخصم تلقائياً على طلبك التالي الذي يتوافق مع شروط الكوبون.
                `)
                .setColor('#4CAF50')
                .addFields(
                    {
                        name: '📋 تفاصيل الكوبون',
                        value: `
• **نوع الخصم:** ${coupon.discount_type === 'percentage' ? 'نسبة مئوية' : 'مبلغ ثابت'}
• **مرات الاستخدام:** ${coupon.used_count}/${coupon.max_uses === -1 ? '∞' : coupon.max_uses}
• **تاريخ الانتهاء:** ${coupon.expires_at ? new Date(coupon.expires_at).toLocaleDateString('ar-EG') : 'غير محدد'}
                        `,
                        inline: false
                    }
                );
            
            // حفظ الكوبون للمستخدم (مؤقتاً)
            // يمكن تطبيقه عند الشراء التالي
            
            const row = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId(`apply_coupon_${coupon.id}`)
                        .setLabel('🛒 تطبيق على طلب جديد')
                        .setStyle(ButtonStyle.Success),
                    new ButtonBuilder()
                        .setCustomId('browse_designs_with_coupon')
                        .setLabel('🎨 تصفح التصاميم')
                        .setStyle(ButtonStyle.Primary),
                    new ButtonBuilder()
                        .setCustomId('coupon_terms')
                        .setLabel('📜 الشروط والأحكام')
                        .setStyle(ButtonStyle.Secondary)
                );
            
            await interaction.reply({ 
                embeds: [validEmbed], 
                components: [row],
                ephemeral: true 
            });
            
        } catch (error) {
            console.error('خطأ في معالجة الكوبون:', error);
            
            const errorEmbed = new EmbedBuilder()
                .setTitle('❌ حدث خطأ')
                .setDescription('عذراً، حدث خطأ أثناء معالجة الكوبون. يرجى المحاولة مرة أخرى.')
                .setColor('#FF0000');
            
            await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
        }
    }
};

// دالة مساعدة لتطبيق الكوبون على طلب
async function applyCouponToOrder(bot, couponId, userId, orderAmount) {
    try {
        const coupon = await bot.database.get(
            'SELECT * FROM coupons WHERE id = ? AND is_active = 1',
            [couponId]
        );
        
        if (!coupon) return { success: false, error: 'كوبون غير صالح' };
        
        // التحقق من الحد الأدنى
        if (coupon.min_amount > 0 && orderAmount < coupon.min_amount) {
            return { 
                success: false, 
                error: `الحد الأدنى للطلب هو ${coupon.min_amount} كريدت` 
            };
        }
        
        // حساب الخصم
        let discountAmount = 0;
        if (coupon.discount_type === 'percentage') {
            discountAmount = Math.floor(orderAmount * (coupon.discount_value / 100));
        } else {
            discountAmount = Math.min(coupon.discount_value, orderAmount);
        }
        
        const finalAmount = orderAmount - discountAmount;
        
        return {
            success: true,
            originalAmount: orderAmount,
            discountAmount: discountAmount,
            finalAmount: finalAmount,
            coupon: coupon
        };
        
    } catch (error) {
        console.error('خطأ في تطبيق الكوبون:', error);
        return { success: false, error: 'خطأ في تطبيق الكوبون' };
    }
}

module.exports.applyCouponToOrder = applyCouponToOrder;
