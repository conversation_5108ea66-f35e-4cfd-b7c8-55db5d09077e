const { Events, Embed<PERSON><PERSON>er, ActionRow<PERSON>uilder, <PERSON>ton<PERSON>uilder, ButtonStyle, ModalBuilder, TextInputBuilder, TextInputStyle } = require('discord.js');

module.exports = {
    name: Events.InteractionCreate,
    async execute(interaction, bot) {
        // التعامل مع الأوامر المائلة
        if (interaction.isChatInputCommand()) {
            const command = bot.commands.get(interaction.commandName);

            if (!command) {
                console.error(`❌ لم يتم العثور على الأمر ${interaction.commandName}`);
                return;
            }

            try {
                await command.execute(interaction, bot);
            } catch (error) {
                console.error('❌ خطأ في تنفيذ الأمر:', error);
                
                const errorEmbed = new EmbedBuilder()
                    .setTitle('❌ حدث خطأ')
                    .setDescription('عذراً، حدث خطأ أثناء تنفيذ الأمر. يرجى المحاولة مرة أخرى.')
                    .setColor('#FF0000')
                    .setTimestamp();

                if (interaction.replied || interaction.deferred) {
                    await interaction.followUp({ embeds: [errorEmbed], ephemeral: true });
                } else {
                    await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
                }
            }
        }

        // التعامل مع الأزرار
        if (interaction.isButton()) {
            try {
                await handleButtonInteraction(interaction, bot);
            } catch (error) {
                console.error('❌ خطأ في التعامل مع الزر:', error);
            }
        }

        // التعامل مع القوائم المنسدلة
        if (interaction.isStringSelectMenu()) {
            try {
                await handleSelectMenuInteraction(interaction, bot);
            } catch (error) {
                console.error('❌ خطأ في التعامل مع القائمة:', error);
            }
        }

        // التعامل مع النماذج
        if (interaction.isModalSubmit()) {
            try {
                await handleModalInteraction(interaction, bot);
            } catch (error) {
                console.error('❌ خطأ في التعامل مع النموذج:', error);
            }
        }
    }
};

async function handleButtonInteraction(interaction, bot) {
    const { customId } = interaction;

    switch (customId) {
        case 'browse_designs':
            await showDesignCategories(interaction, bot);
            break;
            
        case 'my_orders':
            await showUserOrders(interaction, bot);
            break;
            
        case 'custom_order':
            await showCustomOrderForm(interaction, bot);
            break;
            
        case 'support':
            await showSupportPanel(interaction, bot);
            break;
            
        case 'add_to_cart':
            await addToCart(interaction, bot);
            break;
            
        case 'view_cart':
            await showCart(interaction, bot);
            break;
            
        case 'checkout':
            await processCheckout(interaction, bot);
            break;

        case 'credits_info':
            await showCreditsInfo(interaction, bot);
            break;

        case 'featured_designs':
            await showFeaturedDesigns(interaction, bot);
            break;

        case 'new_arrivals':
            await showNewArrivals(interaction, bot);
            break;
            
        default:
            if (customId.startsWith('design_')) {
                await showDesignDetails(interaction, bot, customId.split('_')[1]);
            } else if (customId.startsWith('category_')) {
                await showCategoryDesigns(interaction, bot, customId.split('_')[1]);
            } else if (customId.startsWith('check_payment_')) {
                await checkPaymentStatus(interaction, bot, customId.split('_')[2]);
            } else if (customId.startsWith('cancel_payment_')) {
                await cancelPayment(interaction, bot, customId.split('_')[2]);
            } else if (customId.startsWith('buy_design_')) {
                await buyDesign(interaction, bot, customId.split('_')[2]);
            }
            break;
    }
}

async function showDesignCategories(interaction, bot) {
    const embed = new EmbedBuilder()
        .setTitle('🎨 تصنيفات التصاميم')
        .setDescription('اختر التصنيف الذي تريد تصفحه:')
        .setColor('#4CAF50')
        .addFields(
            { name: '🏷️ اللوجوهات', value: 'تصاميم لوجوهات احترافية', inline: true },
            { name: '🖼️ البانرات', value: 'بانرات لجميع المنصات', inline: true },
            { name: '📱 السوشيال ميديا', value: 'تصاميم للشبكات الاجتماعية', inline: true },
            { name: '🌐 مواقع الويب', value: 'تصاميم واجهات المواقع', inline: true },
            { name: '📄 المطبوعات', value: 'تصاميم للطباعة', inline: true },
            { name: '🎮 الألعاب', value: 'تصاميم متعلقة بالألعاب', inline: true }
        )
        .setFooter({ text: 'اختر تصنيف لعرض التصاميم المتاحة' });

    const row = new ActionRowBuilder()
        .addComponents(
            new ButtonBuilder()
                .setCustomId('category_logos')
                .setLabel('🏷️ اللوجوهات')
                .setStyle(ButtonStyle.Primary),
            new ButtonBuilder()
                .setCustomId('category_banners')
                .setLabel('🖼️ البانرات')
                .setStyle(ButtonStyle.Primary),
            new ButtonBuilder()
                .setCustomId('category_social')
                .setLabel('📱 السوشيال')
                .setStyle(ButtonStyle.Primary)
        );

    const row2 = new ActionRowBuilder()
        .addComponents(
            new ButtonBuilder()
                .setCustomId('category_web')
                .setLabel('🌐 الويب')
                .setStyle(ButtonStyle.Primary),
            new ButtonBuilder()
                .setCustomId('category_print')
                .setLabel('📄 المطبوعات')
                .setStyle(ButtonStyle.Primary),
            new ButtonBuilder()
                .setCustomId('category_gaming')
                .setLabel('🎮 الألعاب')
                .setStyle(ButtonStyle.Primary)
        );

    await interaction.reply({ 
        embeds: [embed], 
        components: [row, row2],
        ephemeral: true 
    });
}

async function showUserOrders(interaction, bot) {
    // جلب طلبات المستخدم من قاعدة البيانات
    const userId = interaction.user.id;
    
    const embed = new EmbedBuilder()
        .setTitle('📦 طلباتي')
        .setDescription('هنا ستجد جميع طلباتك السابقة والحالية')
        .setColor('#2196F3')
        .addFields(
            { name: '🔄 قيد المعالجة', value: '0 طلب', inline: true },
            { name: '✅ مكتملة', value: '0 طلب', inline: true },
            { name: '💰 إجمالي المشتريات', value: '$0.00', inline: true }
        )
        .setFooter({ text: 'لعرض تفاصيل طلب معين، اضغط على الزر المناسب' });

    const row = new ActionRowBuilder()
        .addComponents(
            new ButtonBuilder()
                .setCustomId('order_history')
                .setLabel('📋 تاريخ الطلبات')
                .setStyle(ButtonStyle.Secondary),
            new ButtonBuilder()
                .setCustomId('download_files')
                .setLabel('⬇️ تحميل الملفات')
                .setStyle(ButtonStyle.Success),
            new ButtonBuilder()
                .setCustomId('track_order')
                .setLabel('🔍 تتبع طلب')
                .setStyle(ButtonStyle.Primary)
        );

    await interaction.reply({ 
        embeds: [embed], 
        components: [row],
        ephemeral: true 
    });
}

async function showCustomOrderForm(interaction, bot) {
    const modal = new ModalBuilder()
        .setCustomId('custom_order_modal')
        .setTitle('✨ طلب تصميم مخصص');

    const titleInput = new TextInputBuilder()
        .setCustomId('design_title')
        .setLabel('عنوان التصميم')
        .setStyle(TextInputStyle.Short)
        .setPlaceholder('مثال: لوجو لشركة تقنية')
        .setRequired(true);

    const descriptionInput = new TextInputBuilder()
        .setCustomId('design_description')
        .setLabel('وصف مفصل للتصميم')
        .setStyle(TextInputStyle.Paragraph)
        .setPlaceholder('اكتب وصفاً مفصلاً لما تريده...')
        .setRequired(true);

    const budgetInput = new TextInputBuilder()
        .setCustomId('design_budget')
        .setLabel('الميزانية المتوقعة (بالدولار)')
        .setStyle(TextInputStyle.Short)
        .setPlaceholder('مثال: 50')
        .setRequired(true);

    const deadlineInput = new TextInputBuilder()
        .setCustomId('design_deadline')
        .setLabel('الموعد النهائي المطلوب')
        .setStyle(TextInputStyle.Short)
        .setPlaceholder('مثال: خلال 3 أيام')
        .setRequired(true);

    modal.addComponents(
        new ActionRowBuilder().addComponents(titleInput),
        new ActionRowBuilder().addComponents(descriptionInput),
        new ActionRowBuilder().addComponents(budgetInput),
        new ActionRowBuilder().addComponents(deadlineInput)
    );

    await interaction.showModal(modal);
}

async function showSupportPanel(interaction, bot) {
    const embed = new EmbedBuilder()
        .setTitle('🎧 مركز الدعم')
        .setDescription('نحن هنا لمساعدتك! اختر نوع المساعدة التي تحتاجها:')
        .setColor('#9C27B0')
        .addFields(
            { name: '❓ الأسئلة الشائعة', value: 'إجابات للأسئلة الأكثر شيوعاً', inline: true },
            { name: '💬 دردشة مباشرة', value: 'تحدث مع فريق الدعم', inline: true },
            { name: '📧 إرسال تذكرة', value: 'أرسل مشكلتك بالتفصيل', inline: true }
        )
        .setFooter({ text: 'نحن نرد خلال 24 ساعة كحد أقصى' });

    const row = new ActionRowBuilder()
        .addComponents(
            new ButtonBuilder()
                .setCustomId('faq')
                .setLabel('❓ الأسئلة الشائعة')
                .setStyle(ButtonStyle.Secondary),
            new ButtonBuilder()
                .setCustomId('live_chat')
                .setLabel('💬 دردشة مباشرة')
                .setStyle(ButtonStyle.Success),
            new ButtonBuilder()
                .setCustomId('create_ticket')
                .setLabel('📧 إنشاء تذكرة')
                .setStyle(ButtonStyle.Primary)
        );

    await interaction.reply({ 
        embeds: [embed], 
        components: [row],
        ephemeral: true 
    });
}

async function handleSelectMenuInteraction(interaction, bot) {
    // التعامل مع القوائم المنسدلة
}

async function handleModalInteraction(interaction, bot) {
    if (interaction.customId === 'custom_order_modal') {
        const title = interaction.fields.getTextInputValue('design_title');
        const description = interaction.fields.getTextInputValue('design_description');
        const budget = interaction.fields.getTextInputValue('design_budget');
        const deadline = interaction.fields.getTextInputValue('design_deadline');

        // حفظ الطلب في قاعدة البيانات
        // TODO: إضافة منطق حفظ الطلب المخصص

        const embed = new EmbedBuilder()
            .setTitle('✅ تم استلام طلبك!')
            .setDescription('شكراً لك! تم استلام طلب التصميم المخصص وسيتم مراجعته من قبل فريقنا.')
            .addFields(
                { name: '📝 العنوان', value: title, inline: false },
                { name: '💰 الميزانية', value: `$${budget}`, inline: true },
                { name: '⏰ الموعد النهائي', value: deadline, inline: true }
            )
            .setColor('#4CAF50')
            .setFooter({ text: 'سنتواصل معك خلال 24 ساعة' });

        await interaction.reply({ embeds: [embed], ephemeral: true });
    }
}

// عرض معلومات الكريدت
async function showCreditsInfo(interaction, bot) {
    const embed = new EmbedBuilder()
        .setTitle('💳 معلومات الدفع بالكريدت')
        .setDescription(`
**كيفية الدفع بعملة الكريدت:**

**🔹 ما هي عملة الكريدت؟**
عملة الكريدت هي العملة الرسمية لبوت ProBot، يمكنك استخدامها للدفع في متجرنا بأمان تام.

**🔹 كيفية الحصول على الكريدت:**
• شراء من متجر ProBot الرسمي
• كسب من الأنشطة اليومية في السيرفرات
• الحصول عليها كهدايا من الأصدقاء
• المشاركة في المسابقات والفعاليات

**🔹 مميزات الدفع بالكريدت:**
✅ **أمان 100%** - محمي بواسطة ProBot
✅ **تحقق تلقائي** - لا حاجة لانتظار التأكيد اليدوي
✅ **سرعة فائقة** - تسليم فوري بعد الدفع
✅ **بدون رسوم** - لا توجد رسوم إضافية

**🔹 خطوات الدفع:**
1️⃣ اختر التصميم المطلوب
2️⃣ اضغط على "شراء الآن"
3️⃣ انسخ أمر الدفع المعروض
4️⃣ ألصقه في أي قناة بها ProBot
5️⃣ احصل على تصميمك فوراً!

**🔹 أسعار التصاميم:**
• لوجو بسيط: 500-1000 كريدت
• لوجو متقدم: 1500-2500 كريدت
• بانر: 800-1500 كريدت
• تصميم مخصص: 2000-5000 كريدت
        `)
        .setColor('#00D4AA')
        .addFields(
            { name: '💰 رصيدك الحالي', value: 'استخدم `/balance` لمعرفة رصيدك', inline: true },
            { name: '🛒 طلباتك', value: 'استخدم `/orders` لعرض طلباتك', inline: true },
            { name: '🎁 نقاط الولاء', value: 'اكسب نقاط مع كل شراء', inline: true }
        )
        .setFooter({ text: 'نظام دفع آمن ومضمون • ProBot Credits' })
        .setTimestamp();

    const row = new ActionRowBuilder()
        .addComponents(
            new ButtonBuilder()
                .setCustomId('check_balance')
                .setLabel('💰 تحقق من الرصيد')
                .setStyle(ButtonStyle.Primary),
            new ButtonBuilder()
                .setCustomId('buy_credits')
                .setLabel('🛒 شراء كريدت')
                .setStyle(ButtonStyle.Success),
            new ButtonBuilder()
                .setCustomId('payment_help')
                .setLabel('❓ مساعدة الدفع')
                .setStyle(ButtonStyle.Secondary)
        );

    await interaction.reply({ embeds: [embed], components: [row], ephemeral: true });
}

// شراء تصميم
async function buyDesign(interaction, bot, designId) {
    try {
        // جلب معلومات التصميم
        const design = await bot.database.get(
            'SELECT * FROM designs WHERE id = ? AND is_active = 1',
            [designId]
        );

        if (!design) {
            const errorEmbed = new EmbedBuilder()
                .setTitle('❌ خطأ')
                .setDescription('التصميم غير موجود أو غير متاح حالياً.')
                .setColor('#FF0000');

            return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
        }

        // إنشاء طلب جديد
        const orderResult = await bot.database.run(
            `INSERT INTO orders (user_id, design_id, order_type, total_amount, status)
             VALUES ((SELECT id FROM users WHERE discord_id = ?), ?, 'design', ?, 'pending')`,
            [interaction.user.id, designId, design.price]
        );

        const orderId = orderResult.id;

        // إنشاء طلب دفع
        const paymentData = await bot.proBotPayment.createPaymentRequest(
            interaction,
            design.price,
            orderId,
            `شراء تصميم: ${design.title}`
        );

        await interaction.reply(paymentData);

    } catch (error) {
        console.error('خطأ في شراء التصميم:', error);

        const errorEmbed = new EmbedBuilder()
            .setTitle('❌ حدث خطأ')
            .setDescription('عذراً، حدث خطأ أثناء معالجة طلبك. يرجى المحاولة مرة أخرى.')
            .setColor('#FF0000');

        await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
    }
}

// التحقق من حالة الدفع
async function checkPaymentStatus(interaction, bot, paymentId) {
    const result = await bot.proBotPayment.checkPaymentStatus(paymentId);

    let embed;

    switch (result.status) {
        case 'not_found':
            embed = new EmbedBuilder()
                .setTitle('❌ طلب دفع غير موجود')
                .setDescription('لم يتم العثور على طلب الدفع المحدد.')
                .setColor('#FF0000');
            break;

        case 'expired':
            embed = new EmbedBuilder()
                .setTitle('⏰ انتهت صلاحية الدفع')
                .setDescription('انتهت صلاحية طلب الدفع. يرجى إنشاء طلب جديد.')
                .setColor('#FFA500');
            break;

        case 'pending':
            embed = new EmbedBuilder()
                .setTitle('🟡 في انتظار الدفع')
                .setDescription('لم يتم استلام الدفع بعد. تأكد من تنفيذ أمر الدفع في ProBot.')
                .setColor('#FFA500');
            break;

        case 'completed':
            embed = new EmbedBuilder()
                .setTitle('✅ تم الدفع بنجاح')
                .setDescription('تم استلام دفعتك وسيتم تسليم طلبك قريباً!')
                .setColor('#4CAF50');
            break;
    }

    await interaction.reply({ embeds: [embed], ephemeral: true });
}

// إلغاء الدفع
async function cancelPayment(interaction, bot, paymentId) {
    await bot.proBotPayment.cancelPayment(paymentId);

    const embed = new EmbedBuilder()
        .setTitle('❌ تم إلغاء الدفع')
        .setDescription('تم إلغاء طلب الدفع بنجاح.')
        .setColor('#FF0000');

    await interaction.reply({ embeds: [embed], ephemeral: true });
}

// عرض التصاميم المميزة
async function showFeaturedDesigns(interaction, bot) {
    try {
        const designs = await bot.database.all(`
            SELECT * FROM designs
            WHERE is_active = 1 AND rating >= 4.5
            ORDER BY rating DESC, downloads DESC
            LIMIT 8
        `);

        const embed = new EmbedBuilder()
            .setTitle('⭐ التصاميم المميزة')
            .setDescription('أفضل التصاميم الأعلى تقييماً في المتجر:')
            .setColor('#FFD700');

        designs.forEach((design, index) => {
            embed.addFields({
                name: `${index + 1}. ${design.title}`,
                value: `💰 ${design.price} كريدت\n⭐ ${design.rating}/5`,
                inline: true
            });
        });

        await interaction.reply({ embeds: [embed], ephemeral: true });
    } catch (error) {
        await interaction.reply({ content: '❌ حدث خطأ', ephemeral: true });
    }
}

// عرض التصاميم الجديدة
async function showNewArrivals(interaction, bot) {
    try {
        const designs = await bot.database.all(`
            SELECT * FROM designs
            WHERE is_active = 1
            ORDER BY created_at DESC
            LIMIT 8
        `);

        const embed = new EmbedBuilder()
            .setTitle('🆕 وصل حديثاً')
            .setDescription('أحدث التصاميم المضافة للمتجر:')
            .setColor('#4CAF50');

        designs.forEach((design, index) => {
            embed.addFields({
                name: `${index + 1}. ${design.title}`,
                value: `💰 ${design.price} كريدت`,
                inline: true
            });
        });

        await interaction.reply({ embeds: [embed], ephemeral: true });
    } catch (error) {
        await interaction.reply({ content: '❌ حدث خطأ', ephemeral: true });
    }
}
