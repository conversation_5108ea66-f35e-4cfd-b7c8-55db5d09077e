const { Events, Embed<PERSON><PERSON>er, ActionRow<PERSON>uilder, <PERSON>ton<PERSON>uilder, ButtonStyle, ModalBuilder, TextInputBuilder, TextInputStyle } = require('discord.js');

module.exports = {
    name: Events.InteractionCreate,
    async execute(interaction, bot) {
        // التعامل مع الأوامر المائلة
        if (interaction.isChatInputCommand()) {
            const command = bot.commands.get(interaction.commandName);

            if (!command) {
                console.error(`❌ لم يتم العثور على الأمر ${interaction.commandName}`);
                return;
            }

            try {
                await command.execute(interaction, bot);
            } catch (error) {
                console.error('❌ خطأ في تنفيذ الأمر:', error);
                
                const errorEmbed = new EmbedBuilder()
                    .setTitle('❌ حدث خطأ')
                    .setDescription('عذراً، حدث خطأ أثناء تنفيذ الأمر. يرجى المحاولة مرة أخرى.')
                    .setColor('#FF0000')
                    .setTimestamp();

                if (interaction.replied || interaction.deferred) {
                    await interaction.followUp({ embeds: [errorEmbed], ephemeral: true });
                } else {
                    await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
                }
            }
        }

        // التعامل مع الأزرار
        if (interaction.isButton()) {
            try {
                await handleButtonInteraction(interaction, bot);
            } catch (error) {
                console.error('❌ خطأ في التعامل مع الزر:', error);
            }
        }

        // التعامل مع القوائم المنسدلة
        if (interaction.isStringSelectMenu()) {
            try {
                await handleSelectMenuInteraction(interaction, bot);
            } catch (error) {
                console.error('❌ خطأ في التعامل مع القائمة:', error);
            }
        }

        // التعامل مع النماذج
        if (interaction.isModalSubmit()) {
            try {
                await handleModalInteraction(interaction, bot);
            } catch (error) {
                console.error('❌ خطأ في التعامل مع النموذج:', error);
            }
        }
    }
};

async function handleButtonInteraction(interaction, bot) {
    const { customId } = interaction;

    switch (customId) {
        case 'browse_designs':
            await showDesignCategories(interaction, bot);
            break;
            
        case 'my_orders':
            await showUserOrders(interaction, bot);
            break;
            
        case 'custom_order':
            await showCustomOrderForm(interaction, bot);
            break;
            
        case 'support':
            await showSupportPanel(interaction, bot);
            break;
            
        case 'add_to_cart':
            await addToCart(interaction, bot);
            break;
            
        case 'view_cart':
            await showCart(interaction, bot);
            break;
            
        case 'checkout':
            await processCheckout(interaction, bot);
            break;
            
        default:
            if (customId.startsWith('design_')) {
                await showDesignDetails(interaction, bot, customId.split('_')[1]);
            } else if (customId.startsWith('category_')) {
                await showCategoryDesigns(interaction, bot, customId.split('_')[1]);
            }
            break;
    }
}

async function showDesignCategories(interaction, bot) {
    const embed = new EmbedBuilder()
        .setTitle('🎨 تصنيفات التصاميم')
        .setDescription('اختر التصنيف الذي تريد تصفحه:')
        .setColor('#4CAF50')
        .addFields(
            { name: '🏷️ اللوجوهات', value: 'تصاميم لوجوهات احترافية', inline: true },
            { name: '🖼️ البانرات', value: 'بانرات لجميع المنصات', inline: true },
            { name: '📱 السوشيال ميديا', value: 'تصاميم للشبكات الاجتماعية', inline: true },
            { name: '🌐 مواقع الويب', value: 'تصاميم واجهات المواقع', inline: true },
            { name: '📄 المطبوعات', value: 'تصاميم للطباعة', inline: true },
            { name: '🎮 الألعاب', value: 'تصاميم متعلقة بالألعاب', inline: true }
        )
        .setFooter({ text: 'اختر تصنيف لعرض التصاميم المتاحة' });

    const row = new ActionRowBuilder()
        .addComponents(
            new ButtonBuilder()
                .setCustomId('category_logos')
                .setLabel('🏷️ اللوجوهات')
                .setStyle(ButtonStyle.Primary),
            new ButtonBuilder()
                .setCustomId('category_banners')
                .setLabel('🖼️ البانرات')
                .setStyle(ButtonStyle.Primary),
            new ButtonBuilder()
                .setCustomId('category_social')
                .setLabel('📱 السوشيال')
                .setStyle(ButtonStyle.Primary)
        );

    const row2 = new ActionRowBuilder()
        .addComponents(
            new ButtonBuilder()
                .setCustomId('category_web')
                .setLabel('🌐 الويب')
                .setStyle(ButtonStyle.Primary),
            new ButtonBuilder()
                .setCustomId('category_print')
                .setLabel('📄 المطبوعات')
                .setStyle(ButtonStyle.Primary),
            new ButtonBuilder()
                .setCustomId('category_gaming')
                .setLabel('🎮 الألعاب')
                .setStyle(ButtonStyle.Primary)
        );

    await interaction.reply({ 
        embeds: [embed], 
        components: [row, row2],
        ephemeral: true 
    });
}

async function showUserOrders(interaction, bot) {
    // جلب طلبات المستخدم من قاعدة البيانات
    const userId = interaction.user.id;
    
    const embed = new EmbedBuilder()
        .setTitle('📦 طلباتي')
        .setDescription('هنا ستجد جميع طلباتك السابقة والحالية')
        .setColor('#2196F3')
        .addFields(
            { name: '🔄 قيد المعالجة', value: '0 طلب', inline: true },
            { name: '✅ مكتملة', value: '0 طلب', inline: true },
            { name: '💰 إجمالي المشتريات', value: '$0.00', inline: true }
        )
        .setFooter({ text: 'لعرض تفاصيل طلب معين، اضغط على الزر المناسب' });

    const row = new ActionRowBuilder()
        .addComponents(
            new ButtonBuilder()
                .setCustomId('order_history')
                .setLabel('📋 تاريخ الطلبات')
                .setStyle(ButtonStyle.Secondary),
            new ButtonBuilder()
                .setCustomId('download_files')
                .setLabel('⬇️ تحميل الملفات')
                .setStyle(ButtonStyle.Success),
            new ButtonBuilder()
                .setCustomId('track_order')
                .setLabel('🔍 تتبع طلب')
                .setStyle(ButtonStyle.Primary)
        );

    await interaction.reply({ 
        embeds: [embed], 
        components: [row],
        ephemeral: true 
    });
}

async function showCustomOrderForm(interaction, bot) {
    const modal = new ModalBuilder()
        .setCustomId('custom_order_modal')
        .setTitle('✨ طلب تصميم مخصص');

    const titleInput = new TextInputBuilder()
        .setCustomId('design_title')
        .setLabel('عنوان التصميم')
        .setStyle(TextInputStyle.Short)
        .setPlaceholder('مثال: لوجو لشركة تقنية')
        .setRequired(true);

    const descriptionInput = new TextInputBuilder()
        .setCustomId('design_description')
        .setLabel('وصف مفصل للتصميم')
        .setStyle(TextInputStyle.Paragraph)
        .setPlaceholder('اكتب وصفاً مفصلاً لما تريده...')
        .setRequired(true);

    const budgetInput = new TextInputBuilder()
        .setCustomId('design_budget')
        .setLabel('الميزانية المتوقعة (بالدولار)')
        .setStyle(TextInputStyle.Short)
        .setPlaceholder('مثال: 50')
        .setRequired(true);

    const deadlineInput = new TextInputBuilder()
        .setCustomId('design_deadline')
        .setLabel('الموعد النهائي المطلوب')
        .setStyle(TextInputStyle.Short)
        .setPlaceholder('مثال: خلال 3 أيام')
        .setRequired(true);

    modal.addComponents(
        new ActionRowBuilder().addComponents(titleInput),
        new ActionRowBuilder().addComponents(descriptionInput),
        new ActionRowBuilder().addComponents(budgetInput),
        new ActionRowBuilder().addComponents(deadlineInput)
    );

    await interaction.showModal(modal);
}

async function showSupportPanel(interaction, bot) {
    const embed = new EmbedBuilder()
        .setTitle('🎧 مركز الدعم')
        .setDescription('نحن هنا لمساعدتك! اختر نوع المساعدة التي تحتاجها:')
        .setColor('#9C27B0')
        .addFields(
            { name: '❓ الأسئلة الشائعة', value: 'إجابات للأسئلة الأكثر شيوعاً', inline: true },
            { name: '💬 دردشة مباشرة', value: 'تحدث مع فريق الدعم', inline: true },
            { name: '📧 إرسال تذكرة', value: 'أرسل مشكلتك بالتفصيل', inline: true }
        )
        .setFooter({ text: 'نحن نرد خلال 24 ساعة كحد أقصى' });

    const row = new ActionRowBuilder()
        .addComponents(
            new ButtonBuilder()
                .setCustomId('faq')
                .setLabel('❓ الأسئلة الشائعة')
                .setStyle(ButtonStyle.Secondary),
            new ButtonBuilder()
                .setCustomId('live_chat')
                .setLabel('💬 دردشة مباشرة')
                .setStyle(ButtonStyle.Success),
            new ButtonBuilder()
                .setCustomId('create_ticket')
                .setLabel('📧 إنشاء تذكرة')
                .setStyle(ButtonStyle.Primary)
        );

    await interaction.reply({ 
        embeds: [embed], 
        components: [row],
        ephemeral: true 
    });
}

async function handleSelectMenuInteraction(interaction, bot) {
    // التعامل مع القوائم المنسدلة
}

async function handleModalInteraction(interaction, bot) {
    if (interaction.customId === 'custom_order_modal') {
        const title = interaction.fields.getTextInputValue('design_title');
        const description = interaction.fields.getTextInputValue('design_description');
        const budget = interaction.fields.getTextInputValue('design_budget');
        const deadline = interaction.fields.getTextInputValue('design_deadline');

        // حفظ الطلب في قاعدة البيانات
        // TODO: إضافة منطق حفظ الطلب المخصص

        const embed = new EmbedBuilder()
            .setTitle('✅ تم استلام طلبك!')
            .setDescription('شكراً لك! تم استلام طلب التصميم المخصص وسيتم مراجعته من قبل فريقنا.')
            .addFields(
                { name: '📝 العنوان', value: title, inline: false },
                { name: '💰 الميزانية', value: `$${budget}`, inline: true },
                { name: '⏰ الموعد النهائي', value: deadline, inline: true }
            )
            .setColor('#4CAF50')
            .setFooter({ text: 'سنتواصل معك خلال 24 ساعة' });

        await interaction.reply({ embeds: [embed], ephemeral: true });
    }
}
