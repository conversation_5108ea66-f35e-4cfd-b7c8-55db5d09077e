const { <PERSON><PERSON><PERSON><PERSON>mandBuilder, <PERSON><PERSON><PERSON><PERSON><PERSON>, ActionRowBuilder, ButtonBuilder, ButtonStyle, StringSelectMenuBuilder } = require('discord.js');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('search')
        .setDescription('🔍 البحث في التصاميم')
        .addStringOption(option =>
            option.setName('query')
                .setDescription('كلمة البحث')
                .setRequired(true))
        .addStringOption(option =>
            option.setName('category')
                .setDescription('تصنيف التصاميم')
                .setRequired(false)
                .addChoices(
                    { name: '🏷️ اللوجوهات', value: 'logos' },
                    { name: '🖼️ البانرات', value: 'banners' },
                    { name: '📱 السوشيال ميديا', value: 'social' },
                    { name: '🌐 مواقع الويب', value: 'web' },
                    { name: '📄 المطبوعات', value: 'print' },
                    { name: '🎮 الألعاب', value: 'gaming' }
                ))
        .addIntegerOption(option =>
            option.setName('min_price')
                .setDescription('الحد الأدنى للسعر (بالكريدت)')
                .setRequired(false))
        .addIntegerOption(option =>
            option.setName('max_price')
                .setDescription('الحد الأقصى للسعر (بالكريدت)')
                .setRequired(false))
        .addNumberOption(option =>
            option.setName('min_rating')
                .setDescription('الحد الأدنى للتقييم (1-5)')
                .setRequired(false)
                .setMinValue(1)
                .setMaxValue(5)),
    
    async execute(interaction, bot) {
        try {
            const query = interaction.options.getString('query');
            const category = interaction.options.getString('category');
            const minPrice = interaction.options.getInteger('min_price');
            const maxPrice = interaction.options.getInteger('max_price');
            const minRating = interaction.options.getNumber('min_rating');
            
            // بناء استعلام البحث
            let sql = `
                SELECT * FROM designs 
                WHERE is_active = 1 
                AND (title LIKE ? OR description LIKE ? OR tags LIKE ?)
            `;
            const params = [`%${query}%`, `%${query}%`, `%${query}%`];
            
            // إضافة فلاتر إضافية
            if (category) {
                sql += ' AND category = ?';
                params.push(category);
            }
            
            if (minPrice) {
                sql += ' AND price >= ?';
                params.push(minPrice);
            }
            
            if (maxPrice) {
                sql += ' AND price <= ?';
                params.push(maxPrice);
            }
            
            if (minRating) {
                sql += ' AND rating >= ?';
                params.push(minRating);
            }
            
            sql += ' ORDER BY rating DESC, downloads DESC LIMIT 15';
            
            const designs = await bot.database.all(sql, params);
            
            if (designs.length === 0) {
                const noResultsEmbed = new EmbedBuilder()
                    .setTitle('🔍 لا توجد نتائج')
                    .setDescription(`لم يتم العثور على تصاميم تطابق البحث: **"${query}"**`)
                    .setColor('#FFA500')
                    .addFields(
                        {
                            name: '💡 اقتراحات للبحث',
                            value: `
• جرب كلمات مفتاحية أخرى
• تأكد من الإملاء الصحيح
• استخدم كلمات أعم (مثل "لوجو" بدلاً من "لوجو شركة")
• جرب البحث بدون فلاتر
                            `,
                            inline: false
                        },
                        {
                            name: '🎯 بحث سريع',
                            value: 'جرب البحث عن: `لوجو` أو `بانر` أو `تصميم`',
                            inline: false
                        }
                    );
                
                const suggestionsRow = new ActionRowBuilder()
                    .addComponents(
                        new ButtonBuilder()
                            .setCustomId('search_logos')
                            .setLabel('🏷️ بحث في اللوجوهات')
                            .setStyle(ButtonStyle.Primary),
                        new ButtonBuilder()
                            .setCustomId('search_banners')
                            .setLabel('🖼️ بحث في البانرات')
                            .setStyle(ButtonStyle.Primary),
                        new ButtonBuilder()
                            .setCustomId('browse_all')
                            .setLabel('🎨 تصفح الكل')
                            .setStyle(ButtonStyle.Secondary)
                    );
                
                return await interaction.reply({ 
                    embeds: [noResultsEmbed], 
                    components: [suggestionsRow] 
                });
            }
            
            // إنشاء embed للنتائج
            const resultsEmbed = new EmbedBuilder()
                .setTitle('🔍 نتائج البحث')
                .setDescription(`
**البحث عن:** "${query}"
**عدد النتائج:** ${designs.length} تصميم

${category ? `**التصنيف:** ${getCategoryName(category)}` : ''}
${minPrice || maxPrice ? `**نطاق السعر:** ${minPrice || 0} - ${maxPrice || '∞'} كريدت` : ''}
${minRating ? `**التقييم الأدنى:** ${minRating}/5 ⭐` : ''}
                `)
                .setColor('#4CAF50')
                .setFooter({ text: `الصفحة 1 من 1 • ${designs.length} نتيجة` })
                .setTimestamp();
            
            // إضافة أفضل 6 نتائج كـ fields
            const topResults = designs.slice(0, 6);
            topResults.forEach((design, index) => {
                const priceText = design.discount_price ? 
                    `~~${design.price}~~ **${design.discount_price}** كريدت` : 
                    `**${design.price}** كريدت`;
                
                resultsEmbed.addFields({
                    name: `${index + 1}. ${design.title}`,
                    value: `💰 ${priceText}\n⭐ ${design.rating}/5 (${design.reviews_count} تقييم)\n📥 ${design.downloads} تحميل\n🏷️ ${getCategoryName(design.category)}`,
                    inline: true
                });
            });
            
            // إنشاء قائمة منسدلة للنتائج
            const selectMenu = new StringSelectMenuBuilder()
                .setCustomId('select_search_result')
                .setPlaceholder('اختر تصميماً لعرض التفاصيل...')
                .addOptions(
                    designs.slice(0, 10).map((design, index) => ({
                        label: design.title.length > 100 ? design.title.substring(0, 97) + '...' : design.title,
                        description: `${design.price} كريدت • ${design.category} • ⭐${design.rating}`,
                        value: `design_${design.id}`,
                        emoji: getCategoryEmoji(design.category)
                    }))
                );
            
            const selectRow = new ActionRowBuilder().addComponents(selectMenu);
            
            // أزرار التحكم
            const controlRow = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId('refine_search')
                        .setLabel('🔧 تحسين البحث')
                        .setStyle(ButtonStyle.Secondary),
                    new ButtonBuilder()
                        .setCustomId('sort_results')
                        .setLabel('📊 ترتيب النتائج')
                        .setStyle(ButtonStyle.Secondary),
                    new ButtonBuilder()
                        .setCustomId('save_search')
                        .setLabel('💾 حفظ البحث')
                        .setStyle(ButtonStyle.Primary)
                );
            
            // إضافة زر "عرض المزيد" إذا كان هناك نتائج أكثر
            if (designs.length > 10) {
                controlRow.addComponents(
                    new ButtonBuilder()
                        .setCustomId('show_more_results')
                        .setLabel(`📄 عرض المزيد (${designs.length - 10}+)`)
                        .setStyle(ButtonStyle.Success)
                );
            }
            
            await interaction.reply({ 
                embeds: [resultsEmbed], 
                components: [selectRow, controlRow] 
            });
            
        } catch (error) {
            console.error('خطأ في البحث:', error);
            
            const errorEmbed = new EmbedBuilder()
                .setTitle('❌ حدث خطأ في البحث')
                .setDescription('عذراً، حدث خطأ أثناء البحث. يرجى المحاولة مرة أخرى.')
                .setColor('#FF0000');
            
            await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
        }
    }
};

function getCategoryName(category) {
    const categories = {
        'logos': 'اللوجوهات',
        'banners': 'البانرات',
        'social': 'السوشيال ميديا',
        'web': 'مواقع الويب',
        'print': 'المطبوعات',
        'gaming': 'الألعاب'
    };
    return categories[category] || category;
}

function getCategoryEmoji(category) {
    const emojis = {
        'logos': '🏷️',
        'banners': '🖼️',
        'social': '📱',
        'web': '🌐',
        'print': '📄',
        'gaming': '🎮'
    };
    return emojis[category] || '🎨';
}
