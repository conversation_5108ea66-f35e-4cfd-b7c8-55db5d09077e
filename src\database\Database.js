const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const fs = require('fs');

class Database {
    constructor() {
        this.dbPath = process.env.DATABASE_PATH || './data/database.sqlite';
        this.db = null;
    }

    async init() {
        try {
            // إنشاء مجلد البيانات إذا لم يكن موجوداً
            const dataDir = path.dirname(this.dbPath);
            if (!fs.existsSync(dataDir)) {
                fs.mkdirSync(dataDir, { recursive: true });
            }

            // الاتصال بقاعدة البيانات
            this.db = new sqlite3.Database(this.dbPath);
            
            // إنشاء الجداول
            await this.createTables();
            
            console.log('✅ تم الاتصال بقاعدة البيانات بنجاح');
        } catch (error) {
            console.error('❌ خطأ في الاتصال بقاعدة البيانات:', error);
            throw error;
        }
    }

    async createTables() {
        const tables = [
            // جدول المستخدمين
            `CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                discord_id TEXT UNIQUE NOT NULL,
                username TEXT NOT NULL,
                email TEXT,
                balance REAL DEFAULT 0,
                total_spent REAL DEFAULT 0,
                loyalty_points INTEGER DEFAULT 0,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )`,

            // جدول التصاميم
            `CREATE TABLE IF NOT EXISTS designs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                title TEXT NOT NULL,
                description TEXT,
                category TEXT NOT NULL,
                price REAL NOT NULL,
                discount_price REAL,
                file_path TEXT NOT NULL,
                preview_path TEXT,
                tags TEXT,
                downloads INTEGER DEFAULT 0,
                rating REAL DEFAULT 0,
                reviews_count INTEGER DEFAULT 0,
                is_active BOOLEAN DEFAULT 1,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )`,

            // جدول الطلبات
            `CREATE TABLE IF NOT EXISTS orders (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                design_id INTEGER,
                order_type TEXT NOT NULL, -- 'design' or 'custom'
                status TEXT DEFAULT 'pending', -- pending, processing, completed, cancelled
                total_amount REAL NOT NULL,
                payment_method TEXT,
                payment_id TEXT,
                custom_requirements TEXT,
                delivery_date DATETIME,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id),
                FOREIGN KEY (design_id) REFERENCES designs (id)
            )`,

            // جدول المراجعات
            `CREATE TABLE IF NOT EXISTS reviews (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                design_id INTEGER NOT NULL,
                order_id INTEGER NOT NULL,
                rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
                comment TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id),
                FOREIGN KEY (design_id) REFERENCES designs (id),
                FOREIGN KEY (order_id) REFERENCES orders (id)
            )`,

            // جدول كوبونات الخصم
            `CREATE TABLE IF NOT EXISTS coupons (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                code TEXT UNIQUE NOT NULL,
                discount_type TEXT NOT NULL, -- 'percentage' or 'fixed'
                discount_value REAL NOT NULL,
                min_amount REAL DEFAULT 0,
                max_uses INTEGER DEFAULT -1, -- -1 for unlimited
                used_count INTEGER DEFAULT 0,
                expires_at DATETIME,
                is_active BOOLEAN DEFAULT 1,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )`,

            // جدول استخدام الكوبونات
            `CREATE TABLE IF NOT EXISTS coupon_usage (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                coupon_id INTEGER NOT NULL,
                user_id INTEGER NOT NULL,
                order_id INTEGER NOT NULL,
                used_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (coupon_id) REFERENCES coupons (id),
                FOREIGN KEY (user_id) REFERENCES users (id),
                FOREIGN KEY (order_id) REFERENCES orders (id)
            )`,

            // جدول الإحالات
            `CREATE TABLE IF NOT EXISTS referrals (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                referrer_id INTEGER NOT NULL,
                referred_id INTEGER NOT NULL,
                commission_earned REAL DEFAULT 0,
                status TEXT DEFAULT 'pending', -- pending, paid
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (referrer_id) REFERENCES users (id),
                FOREIGN KEY (referred_id) REFERENCES users (id)
            )`,

            // جدول الإعدادات
            `CREATE TABLE IF NOT EXISTS settings (
                key TEXT PRIMARY KEY,
                value TEXT NOT NULL,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )`
        ];

        for (const table of tables) {
            await this.run(table);
        }

        // إدراج الإعدادات الافتراضية
        await this.insertDefaultSettings();
    }

    async insertDefaultSettings() {
        const defaultSettings = [
            ['commission_rate', '0.10'], // 10% عمولة للمسوقين
            ['min_withdrawal', '50'], // الحد الأدنى للسحب
            ['currency', 'USD'],
            ['tax_rate', '0.00'], // معدل الضريبة
            ['welcome_message', 'مرحباً بك في متجر التصاميم!'],
            ['support_channel', ''], // قناة الدعم
            ['announcement_channel', ''] // قناة الإعلانات
        ];

        for (const [key, value] of defaultSettings) {
            await this.run(
                'INSERT OR IGNORE INTO settings (key, value) VALUES (?, ?)',
                [key, value]
            );
        }
    }

    // تشغيل استعلام
    run(sql, params = []) {
        return new Promise((resolve, reject) => {
            this.db.run(sql, params, function(err) {
                if (err) {
                    reject(err);
                } else {
                    resolve({ id: this.lastID, changes: this.changes });
                }
            });
        });
    }

    // جلب صف واحد
    get(sql, params = []) {
        return new Promise((resolve, reject) => {
            this.db.get(sql, params, (err, row) => {
                if (err) {
                    reject(err);
                } else {
                    resolve(row);
                }
            });
        });
    }

    // جلب عدة صفوف
    all(sql, params = []) {
        return new Promise((resolve, reject) => {
            this.db.all(sql, params, (err, rows) => {
                if (err) {
                    reject(err);
                } else {
                    resolve(rows);
                }
            });
        });
    }

    // إغلاق الاتصال
    close() {
        return new Promise((resolve, reject) => {
            this.db.close((err) => {
                if (err) {
                    reject(err);
                } else {
                    resolve();
                }
            });
        });
    }
}

module.exports = Database;
