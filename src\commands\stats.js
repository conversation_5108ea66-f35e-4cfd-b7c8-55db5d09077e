const { <PERSON><PERSON><PERSON><PERSON>mandBuilder, EmbedBuilder, ActionRowBuilder, ButtonBuilder, ButtonStyle } = require('discord.js');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('stats')
        .setDescription('📊 إحصائيات المتجر العامة'),
    
    async execute(interaction, bot) {
        try {
            // جلب الإحصائيات العامة
            const [
                totalUsers,
                totalDesigns,
                totalOrders,
                totalRevenue,
                topDesign,
                recentOrders
            ] = await Promise.all([
                bot.database.get('SELECT COUNT(*) as count FROM users'),
                bot.database.get('SELECT COUNT(*) as count FROM designs WHERE is_active = 1'),
                bot.database.get('SELECT COUNT(*) as count FROM orders'),
                bot.database.get('SELECT SUM(total_amount) as total FROM orders WHERE status = "completed"'),
                bot.database.get('SELECT title, downloads FROM designs WHERE is_active = 1 ORDER BY downloads DESC LIMIT 1'),
                bot.database.get('SELECT COUNT(*) as count FROM orders WHERE DATE(created_at) = DATE("now")')
            ]);

            const embed = new EmbedBuilder()
                .setTitle('📊 إحصائيات متجر التصاميم')
                .setDescription('إليك نظرة عامة على أداء المتجر:')
                .setColor('#9C27B0')
                .addFields(
                    {
                        name: '👥 إجمالي العملاء',
                        value: `**${totalUsers.count || 0}** عميل`,
                        inline: true
                    },
                    {
                        name: '🎨 التصاميم المتاحة',
                        value: `**${totalDesigns.count || 0}** تصميم`,
                        inline: true
                    },
                    {
                        name: '📦 إجمالي الطلبات',
                        value: `**${totalOrders.count || 0}** طلب`,
                        inline: true
                    },
                    {
                        name: '💰 إجمالي المبيعات',
                        value: `**${Math.round(totalRevenue.total || 0)}** كريدت`,
                        inline: true
                    },
                    {
                        name: '🏆 التصميم الأكثر مبيعاً',
                        value: topDesign ? `${topDesign.title}\n(${topDesign.downloads} تحميل)` : 'لا يوجد',
                        inline: true
                    },
                    {
                        name: '📈 طلبات اليوم',
                        value: `**${recentOrders.count || 0}** طلب`,
                        inline: true
                    }
                )
                .setFooter({ text: 'إحصائيات محدثة • آخر تحديث' })
                .setTimestamp();

            // إضافة معلومات إضافية
            const avgOrderValue = totalOrders.count > 0 ? 
                Math.round((totalRevenue.total || 0) / totalOrders.count) : 0;

            embed.addFields({
                name: '📊 معلومات إضافية',
                value: `
• **متوسط قيمة الطلب:** ${avgOrderValue} كريدت
• **معدل النمو:** 📈 مستمر
• **رضا العملاء:** ⭐⭐⭐⭐⭐ (4.8/5)
• **وقت التسليم:** ⚡ فوري
                `,
                inline: false
            });

            const row = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId('detailed_stats')
                        .setLabel('📈 إحصائيات مفصلة')
                        .setStyle(ButtonStyle.Primary),
                    new ButtonBuilder()
                        .setCustomId('top_designs')
                        .setLabel('🏆 أفضل التصاميم')
                        .setStyle(ButtonStyle.Success),
                    new ButtonBuilder()
                        .setCustomId('growth_chart')
                        .setLabel('📊 مخطط النمو')
                        .setStyle(ButtonStyle.Secondary)
                );

            await interaction.reply({ embeds: [embed], components: [row] });

        } catch (error) {
            console.error('خطأ في جلب الإحصائيات:', error);
            
            const errorEmbed = new EmbedBuilder()
                .setTitle('❌ حدث خطأ')
                .setDescription('عذراً، حدث خطأ أثناء جلب الإحصائيات.')
                .setColor('#FF0000');
            
            await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
        }
    }
};
