// اختبار بسيط للبوت
console.log('🔄 بدء اختبار البوت...');

const { Client, GatewayIntentBits } = require('discord.js');
require('dotenv').config();

const client = new Client({
    intents: [GatewayIntentBits.Guilds, GatewayIntentBits.GuildMessages]
});

client.once('ready', () => {
    console.log('✅ البوت متصل بنجاح!');
    console.log(`🤖 اسم البوت: ${client.user.tag}`);
    console.log(`📊 السيرفرات: ${client.guilds.cache.size}`);
    console.log('🎉 البوت جاهز للاستخدام!');
});

client.on('error', (error) => {
    console.error('❌ خطأ في البوت:', error);
});

if (!process.env.DISCORD_TOKEN) {
    console.error('❌ لم يتم العثور على DISCORD_TOKEN');
    process.exit(1);
}

console.log('🔑 جاري تسجيل الدخول...');
client.login(process.env.DISCORD_TOKEN)
    .then(() => {
        console.log('✅ تم تسجيل الدخول بنجاح');
    })
    .catch(error => {
        console.error('❌ فشل تسجيل الدخول:', error);
    });
