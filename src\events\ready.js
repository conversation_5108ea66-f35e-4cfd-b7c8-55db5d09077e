const { Events, ActivityType } = require('discord.js');

module.exports = {
    name: Events.ClientReady,
    once: true,
    async execute(client, bot) {
        console.log(`🚀 البوت جاهز! تم تسجيل الدخول باسم ${client.user.tag}`);
        
        // تعيين حالة البوت
        client.user.setActivity('🎨 متجر التصاميم | /shop', { 
            type: ActivityType.Playing 
        });

        // تسجيل الأوامر
        try {
            const commands = Array.from(bot.commands.values()).map(command => command.data.toJSON());
            
            if (process.env.GUILD_ID) {
                // تسجيل الأوامر في السيرفر المحدد (للتطوير)
                const guild = client.guilds.cache.get(process.env.GUILD_ID);
                if (guild) {
                    await guild.commands.set(commands);
                    console.log(`✅ تم تسجيل ${commands.length} أمر في السيرفر ${guild.name}`);
                }
            } else {
                // تسجيل الأوامر عالمياً
                await client.application.commands.set(commands);
                console.log(`✅ تم تسجيل ${commands.length} أمر عالمياً`);
            }
        } catch (error) {
            console.error('❌ خطأ في تسجيل الأوامر:', error);
        }

        // إنشاء المجلدات المطلوبة
        const fs = require('fs');
        const path = require('path');
        
        const directories = [
            './designs',
            './designs/logos',
            './designs/banners', 
            './designs/social',
            './designs/web',
            './temp',
            './data',
            './uploads'
        ];

        for (const dir of directories) {
            if (!fs.existsSync(dir)) {
                fs.mkdirSync(dir, { recursive: true });
                console.log(`📁 تم إنشاء المجلد: ${dir}`);
            }
        }

        // إحصائيات البوت
        console.log(`📊 إحصائيات البوت:`);
        console.log(`   • السيرفرات: ${client.guilds.cache.size}`);
        console.log(`   • المستخدمين: ${client.users.cache.size}`);
        console.log(`   • الأوامر: ${bot.commands.size}`);
        
        // بدء المهام المجدولة
        bot.startScheduledTasks();
    }
};
