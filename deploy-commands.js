// تسجيل أوامر الـ Slash Commands
const { REST, Routes } = require('discord.js');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

const commands = [];

// تحميل جميع الأوامر
const commandsPath = path.join(__dirname, 'src', 'commands');
const commandFiles = fs.readdirSync(commandsPath).filter(file => file.endsWith('.js'));

console.log('📋 تحميل الأوامر...');

for (const file of commandFiles) {
    try {
        const filePath = path.join(commandsPath, file);
        const command = require(filePath);
        
        if ('data' in command && 'execute' in command) {
            commands.push(command.data.toJSON());
            console.log(`✅ تم تحميل الأمر: ${command.data.name}`);
        } else {
            console.log(`⚠️ الأمر في ${file} مفقود خاصية "data" أو "execute"`);
        }
    } catch (error) {
        console.error(`❌ خطأ في تحميل ${file}:`, error.message);
    }
}

console.log(`📊 إجمالي الأوامر المحملة: ${commands.length}`);

// إنشاء REST instance
const rest = new REST().setToken(process.env.DISCORD_TOKEN);

// تسجيل الأوامر
(async () => {
    try {
        console.log(`🔄 بدء تسجيل ${commands.length} أمر...`);

        let data;
        
        if (process.env.GUILD_ID) {
            // تسجيل في سيرفر محدد (للتطوير - أسرع)
            data = await rest.put(
                Routes.applicationGuildCommands(process.env.CLIENT_ID, process.env.GUILD_ID),
                { body: commands }
            );
            console.log(`✅ تم تسجيل ${data.length} أمر في السيرفر بنجاح!`);
        } else {
            // تسجيل عالمي (يستغرق وقت أطول)
            data = await rest.put(
                Routes.applicationCommands(process.env.CLIENT_ID),
                { body: commands }
            );
            console.log(`✅ تم تسجيل ${data.length} أمر عالمياً بنجاح!`);
        }

        console.log('🎉 تم تسجيل جميع الأوامر بنجاح!');
        console.log('\n📋 الأوامر المتاحة:');
        
        data.forEach((cmd, index) => {
            console.log(`${index + 1}. /${cmd.name} - ${cmd.description}`);
        });
        
        console.log('\n💡 يمكنك الآن استخدام الأوامر في ديسكورد!');
        
    } catch (error) {
        console.error('❌ خطأ في تسجيل الأوامر:', error);
    }
})();
