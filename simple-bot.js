const { Client, GatewayIntentBits, <PERSON><PERSON><PERSON><PERSON>er, ActionRowBuilder, ButtonBuilder, ButtonStyle } = require('discord.js');
require('dotenv').config();

const client = new Client({
    intents: [GatewayIntentBits.Guilds, GatewayIntentBits.GuildMessages, GatewayIntentBits.MessageContent]
});

client.once('ready', () => {
    console.log(`✅ البوت شغال! ${client.user.tag}`);
    client.user.setActivity('🎨 متجر التصاميم', { type: 'PLAYING' });
});

// أمر المتجر
client.on('messageCreate', async (message) => {
    if (message.author.bot) return;
    
    if (message.content === '!shop') {
        const embed = new EmbedBuilder()
            .setTitle('🎨 متجر التصاميم')
            .setDescription(`مرحباً ${message.author.displayName}!\n\n🏷️ اللوجوهات: 500-2500 كريدت\n🖼️ البانرات: 300-1500 كريدت\n📱 السوشيال: 200-1000 كريدت`)
            .setColor('#FF6B6B');

        const row = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('browse')
                    .setLabel('🎨 تصفح التصاميم')
                    .setStyle(ButtonStyle.Primary),
                new ButtonBuilder()
                    .setCustomId('orders')
                    .setLabel('📦 طلباتي')
                    .setStyle(ButtonStyle.Secondary),
                new ButtonBuilder()
                    .setCustomId('balance')
                    .setLabel('💰 رصيدي')
                    .setStyle(ButtonStyle.Success)
            );

        await message.reply({ embeds: [embed], components: [row] });
    }
    
    if (message.content === '!help') {
        const embed = new EmbedBuilder()
            .setTitle('📚 أوامر البوت')
            .setDescription('!shop - المتجر\n!help - المساعدة\n!test - اختبار')
            .setColor('#2196F3');
        
        await message.reply({ embeds: [embed] });
    }
    
    if (message.content === '!test') {
        await message.reply('✅ البوت يشتغل تمام!');
    }
});

// معالجة الأزرار
client.on('interactionCreate', async (interaction) => {
    if (!interaction.isButton()) return;
    
    const { customId } = interaction;
    
    if (customId === 'browse') {
        const embed = new EmbedBuilder()
            .setTitle('🎨 التصاميم المتاحة')
            .setDescription('1. لوجو شركة تقنية - 1500 كريدت\n2. بانر يوتيوب - 800 كريدت\n3. بوست انستقرام - 500 كريدت')
            .setColor('#4CAF50');
        
        const row = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('buy_1')
                    .setLabel('💳 شراء لوجو')
                    .setStyle(ButtonStyle.Success),
                new ButtonBuilder()
                    .setCustomId('buy_2')
                    .setLabel('💳 شراء بانر')
                    .setStyle(ButtonStyle.Success)
            );
        
        await interaction.reply({ embeds: [embed], components: [row], ephemeral: true });
    }
    
    if (customId === 'orders') {
        const embed = new EmbedBuilder()
            .setTitle('📦 طلباتي')
            .setDescription('لا توجد طلبات حالياً\nابدأ التسوق الآن!')
            .setColor('#2196F3');
        
        await interaction.reply({ embeds: [embed], ephemeral: true });
    }
    
    if (customId === 'balance') {
        const embed = new EmbedBuilder()
            .setTitle('💰 رصيدي')
            .setDescription('الرصيد الحالي: 0 كريدت\nنقاط الولاء: 0 نقطة')
            .setColor('#FFD700');
        
        await interaction.reply({ embeds: [embed], ephemeral: true });
    }
    
    if (customId.startsWith('buy_')) {
        const embed = new EmbedBuilder()
            .setTitle('💳 طلب دفع')
            .setDescription('لشراء هذا التصميم، استخدم الأمر:\n```#credits @البوت 1500 ABC123```\nفي أي قناة بها ProBot')
            .setColor('#00D4AA');
        
        await interaction.reply({ embeds: [embed], ephemeral: true });
    }
});

client.on('error', console.error);

console.log('🔄 جاري تشغيل البوت...');
client.login(process.env.DISCORD_TOKEN);
