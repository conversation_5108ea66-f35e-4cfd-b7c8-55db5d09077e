const { SlashCommandBuilder, EmbedBuilder, ActionRowBuilder, ButtonBuilder, ButtonStyle } = require('discord.js');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('shop')
        .setDescription('🛍️ افتح متجر التصاميم الرئيسي'),
    
    async execute(interaction, bot) {
        const embed = new EmbedBuilder()
            .setTitle('🎨 متجر التصاميم الاحترافية')
            .setDescription(`
**مرحباً ${interaction.user.displayName}! 👋**

أهلاً بك في أكبر متجر تصاميم على ديسكورد! نقدم لك:

🎯 **تصاميم عالية الجودة** - أكثر من 1000+ تصميم جاهز
💰 **أسعار تنافسية** - ابتداءً من $5 فقط  
⚡ **تسليم فوري** - احصل على تصميمك خلال ثوانٍ
🎨 **تصاميم مخصصة** - نصمم لك حسب طلبك
🏆 **ضمان الجودة** - استرداد كامل إذا لم تكن راضياً

**📊 إحصائيات المتجر:**
• العملاء الراضون: 5,000+ عميل
• التصاميم المباعة: 15,000+ تصميم  
• التقييم: ⭐⭐⭐⭐⭐ (4.9/5)
            `)
            .setColor('#FF6B6B')
            .setThumbnail('https://cdn.discordapp.com/attachments/placeholder.png')
            .addFields(
                { 
                    name: '🏷️ التصنيفات المتاحة', 
                    value: '• اللوجوهات والهويات البصرية\n• بانرات وأغلفة\n• تصاميم السوشيال ميديا\n• واجهات المواقع\n• تصاميم الطباعة\n• تصاميم الألعاب', 
                    inline: true 
                },
                { 
                    name: '💎 العروض الحالية', 
                    value: '• خصم 20% على اللوجوهات\n• اشتري 3 واحصل على 1 مجاناً\n• تصميم مخصص بـ $25 فقط\n• شحن مجاني للطلبات +$50', 
                    inline: true 
                },
                { 
                    name: '🎁 برنامج الولاء', 
                    value: '• اكسب نقاط مع كل شراء\n• خصومات حصرية للأعضاء\n• وصول مبكر للتصاميم الجديدة\n• دعم أولوية', 
                    inline: false 
                }
            )
            .setImage('https://cdn.discordapp.com/attachments/placeholder_banner.png')
            .setFooter({ 
                text: 'متجر التصاميم • جودة عالية • خدمة سريعة • دعم 24/7', 
                iconURL: interaction.client.user.displayAvatarURL() 
            })
            .setTimestamp();

        const row1 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('browse_designs')
                    .setLabel('🎨 تصفح التصاميم')
                    .setStyle(ButtonStyle.Primary)
                    .setEmoji('🎨'),
                new ButtonBuilder()
                    .setCustomId('featured_designs')
                    .setLabel('⭐ التصاميم المميزة')
                    .setStyle(ButtonStyle.Success)
                    .setEmoji('⭐'),
                new ButtonBuilder()
                    .setCustomId('new_arrivals')
                    .setLabel('🆕 وصل حديثاً')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('🆕')
            );

        const row2 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('my_orders')
                    .setLabel('📦 طلباتي')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('📦'),
                new ButtonBuilder()
                    .setCustomId('custom_order')
                    .setLabel('✨ طلب مخصص')
                    .setStyle(ButtonStyle.Success)
                    .setEmoji('✨'),
                new ButtonBuilder()
                    .setCustomId('view_cart')
                    .setLabel('🛒 السلة')
                    .setStyle(ButtonStyle.Primary)
                    .setEmoji('🛒')
            );

        const row3 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('profile')
                    .setLabel('👤 ملفي الشخصي')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('👤'),
                new ButtonBuilder()
                    .setCustomId('support')
                    .setLabel('🎧 الدعم')
                    .setStyle(ButtonStyle.Danger)
                    .setEmoji('🎧'),
                new ButtonBuilder()
                    .setCustomId('about')
                    .setLabel('ℹ️ حول المتجر')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('ℹ️')
            );

        await interaction.reply({ 
            embeds: [embed], 
            components: [row1, row2, row3] 
        });

        // إضافة المستخدم لقاعدة البيانات إذا لم يكن موجوداً
        try {
            const existingUser = await bot.database.get(
                'SELECT * FROM users WHERE discord_id = ?',
                [interaction.user.id]
            );

            if (!existingUser) {
                await bot.database.run(
                    'INSERT INTO users (discord_id, username) VALUES (?, ?)',
                    [interaction.user.id, interaction.user.username]
                );
                
                // إرسال رسالة ترحيب للمستخدم الجديد
                const welcomeEmbed = new EmbedBuilder()
                    .setTitle('🎉 مرحباً بك في متجر التصاميم!')
                    .setDescription(`
**أهلاً وسهلاً ${interaction.user.displayName}!**

شكراً لانضمامك إلى متجرنا! كهدية ترحيب، احصل على:

🎁 **كوبون خصم 15%** - استخدم الكود: \`WELCOME15\`
💎 **50 نقطة ولاء مجانية**
📚 **دليل المبتدئين** - كيفية اختيار التصميم المناسب

**نصائح للبداية:**
• تصفح التصنيفات المختلفة
• اقرأ مراجعات العملاء
• ابدأ بالتصاميم الجاهزة
• لا تتردد في طلب المساعدة

نتمنى لك تجربة رائعة! 🚀
                    `)
                    .setColor('#4CAF50')
                    .setThumbnail(interaction.user.displayAvatarURL())
                    .setFooter({ text: 'مرحباً بك في عائلة متجر التصاميم!' });

                await interaction.followUp({ 
                    embeds: [welcomeEmbed], 
                    ephemeral: true 
                });
            }
        } catch (error) {
            console.error('خطأ في إضافة المستخدم:', error);
        }
    }
};
