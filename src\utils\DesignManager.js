const fs = require('fs');
const path = require('path');
// const sharp = require('sharp'); // تم تعطيلها مؤقتاً

class DesignManager {
    constructor(bot) {
        this.bot = bot;
        this.designsPath = './designs';
        this.tempPath = './temp';
        this.allowedFormats = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg', 'pdf', 'ai', 'psd'];
        this.maxFileSize = 50 * 1024 * 1024; // 50MB
        
        this.ensureDirectories();
    }

    ensureDirectories() {
        const dirs = [
            this.designsPath,
            this.tempPath,
            path.join(this.designsPath, 'logos'),
            path.join(this.designsPath, 'banners'),
            path.join(this.designsPath, 'social'),
            path.join(this.designsPath, 'web'),
            path.join(this.designsPath, 'print'),
            path.join(this.designsPath, 'gaming'),
            path.join(this.designsPath, 'previews')
        ];

        dirs.forEach(dir => {
            if (!fs.existsSync(dir)) {
                fs.mkdirSync(dir, { recursive: true });
            }
        });
    }

    // إضافة تصميم جديد
    async addDesign(designData, files) {
        try {
            const designId = await this.bot.database.run(
                `INSERT INTO designs (title, description, category, price, discount_price, tags, file_path, preview_path) 
                 VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
                [
                    designData.title,
                    designData.description,
                    designData.category,
                    designData.price,
                    designData.discountPrice || null,
                    designData.tags || '',
                    '', // سيتم تحديثه بعد رفع الملف
                    '' // سيتم تحديثه بعد إنشاء المعاينة
                ]
            );

            // معالجة الملفات
            const filePaths = await this.processDesignFiles(designId.id, designData.category, files);
            
            // تحديث مسارات الملفات في قاعدة البيانات
            await this.bot.database.run(
                'UPDATE designs SET file_path = ?, preview_path = ? WHERE id = ?',
                [filePaths.mainFile, filePaths.preview, designId.id]
            );

            this.bot.logger.success(`تم إضافة تصميم جديد: ${designData.title} (ID: ${designId.id})`);
            return designId.id;

        } catch (error) {
            this.bot.logger.error('خطأ في إضافة التصميم:', error);
            throw error;
        }
    }

    // معالجة ملفات التصميم
    async processDesignFiles(designId, category, files) {
        const categoryPath = path.join(this.designsPath, category);
        const designFolder = path.join(categoryPath, `design_${designId}`);
        
        // إنشاء مجلد للتصميم
        if (!fs.existsSync(designFolder)) {
            fs.mkdirSync(designFolder, { recursive: true });
        }

        let mainFilePath = '';
        let previewPath = '';

        // معالجة الملف الرئيسي
        if (files.mainFile) {
            const mainFile = files.mainFile;
            const fileExtension = path.extname(mainFile.originalname);
            const fileName = `main${fileExtension}`;
            mainFilePath = path.join(designFolder, fileName);
            
            // نسخ الملف
            fs.writeFileSync(mainFilePath, mainFile.buffer);
        }

        // معالجة ملف المعاينة أو إنشاؤه
        if (files.preview) {
            const previewFile = files.preview;
            const previewExtension = path.extname(previewFile.originalname);
            const previewFileName = `preview${previewExtension}`;
            previewPath = path.join(designFolder, previewFileName);
            
            fs.writeFileSync(previewPath, previewFile.buffer);
        } else {
            // إنشاء معاينة تلقائية إذا كان الملف الرئيسي صورة
            previewPath = await this.generatePreview(mainFilePath, designFolder);
        }

        return {
            mainFile: mainFilePath,
            preview: previewPath
        };
    }

    // إنشاء معاينة تلقائية
    async generatePreview(mainFilePath, designFolder) {
        try {
            const previewPath = path.join(designFolder, 'preview.jpg');
            
            // التحقق من نوع الملف
            const fileExtension = path.extname(mainFilePath).toLowerCase();
            
            if (['.jpg', '.jpeg', '.png', '.webp'].includes(fileExtension)) {
                // نسخ الصورة كمعاينة (مؤقتاً بدلاً من sharp)
                const fs = require('fs');
                fs.copyFileSync(mainFilePath, previewPath.replace(path.extname(previewPath), '.jpg'));
                return previewPath.replace(path.extname(previewPath), '.jpg');
            } else {
                // للملفات الأخرى، إنشاء صورة افتراضية
                return await this.createDefaultPreview(designFolder, fileExtension);
            }
        } catch (error) {
            this.bot.logger.error('خطأ في إنشاء المعاينة:', error);
            return await this.createDefaultPreview(designFolder, '.unknown');
        }
    }

    // إنشاء معاينة افتراضية
    async createDefaultPreview(designFolder, fileExtension) {
        const previewPath = path.join(designFolder, 'preview.jpg');
        
        // إنشاء صورة بسيطة تحتوي على نوع الملف
        const svg = `
            <svg width="400" height="300" xmlns="http://www.w3.org/2000/svg">
                <rect width="400" height="300" fill="#f0f0f0" stroke="#ddd" stroke-width="2"/>
                <text x="200" y="150" text-anchor="middle" font-family="Arial" font-size="24" fill="#666">
                    ${fileExtension.toUpperCase()} File
                </text>
                <text x="200" y="180" text-anchor="middle" font-family="Arial" font-size="14" fill="#999">
                    Preview not available
                </text>
            </svg>
        `;
        
        // إنشاء ملف نصي بدلاً من الصورة (مؤقتاً)
        fs.writeFileSync(previewPath.replace('.jpg', '.txt'), `Preview for ${fileExtension} file`);
        
        return previewPath;
    }

    // جلب تصميم
    async getDesign(designId) {
        try {
            const design = await this.bot.database.get(
                'SELECT * FROM designs WHERE id = ?',
                [designId]
            );
            
            if (!design) {
                throw new Error('التصميم غير موجود');
            }
            
            return design;
        } catch (error) {
            this.bot.logger.error('خطأ في جلب التصميم:', error);
            throw error;
        }
    }

    // تحديث تصميم
    async updateDesign(designId, updateData) {
        try {
            const fields = [];
            const values = [];
            
            Object.keys(updateData).forEach(key => {
                if (updateData[key] !== undefined) {
                    fields.push(`${key} = ?`);
                    values.push(updateData[key]);
                }
            });
            
            if (fields.length === 0) {
                throw new Error('لا توجد بيانات للتحديث');
            }
            
            values.push(designId);
            
            await this.bot.database.run(
                `UPDATE designs SET ${fields.join(', ')}, updated_at = CURRENT_TIMESTAMP WHERE id = ?`,
                values
            );
            
            this.bot.logger.success(`تم تحديث التصميم: ${designId}`);
            return true;
            
        } catch (error) {
            this.bot.logger.error('خطأ في تحديث التصميم:', error);
            throw error;
        }
    }

    // حذف تصميم
    async deleteDesign(designId) {
        try {
            const design = await this.getDesign(designId);
            
            // حذف الملفات
            if (design.file_path && fs.existsSync(design.file_path)) {
                const designFolder = path.dirname(design.file_path);
                fs.rmSync(designFolder, { recursive: true, force: true });
            }
            
            // حذف من قاعدة البيانات
            await this.bot.database.run(
                'DELETE FROM designs WHERE id = ?',
                [designId]
            );
            
            this.bot.logger.success(`تم حذف التصميم: ${designId}`);
            return true;
            
        } catch (error) {
            this.bot.logger.error('خطأ في حذف التصميم:', error);
            throw error;
        }
    }

    // البحث في التصاميم
    async searchDesigns(query, filters = {}) {
        try {
            let sql = 'SELECT * FROM designs WHERE is_active = 1';
            const params = [];
            
            // البحث النصي
            if (query) {
                sql += ' AND (title LIKE ? OR description LIKE ? OR tags LIKE ?)';
                const searchTerm = `%${query}%`;
                params.push(searchTerm, searchTerm, searchTerm);
            }
            
            // فلترة حسب التصنيف
            if (filters.category) {
                sql += ' AND category = ?';
                params.push(filters.category);
            }
            
            // فلترة حسب السعر
            if (filters.minPrice) {
                sql += ' AND price >= ?';
                params.push(filters.minPrice);
            }
            
            if (filters.maxPrice) {
                sql += ' AND price <= ?';
                params.push(filters.maxPrice);
            }
            
            // ترتيب النتائج
            if (filters.sortBy) {
                switch (filters.sortBy) {
                    case 'price_low':
                        sql += ' ORDER BY price ASC';
                        break;
                    case 'price_high':
                        sql += ' ORDER BY price DESC';
                        break;
                    case 'popular':
                        sql += ' ORDER BY downloads DESC';
                        break;
                    case 'rating':
                        sql += ' ORDER BY rating DESC';
                        break;
                    default:
                        sql += ' ORDER BY created_at DESC';
                }
            } else {
                sql += ' ORDER BY created_at DESC';
            }
            
            // تحديد عدد النتائج
            const limit = filters.limit || 20;
            sql += ' LIMIT ?';
            params.push(limit);
            
            const designs = await this.bot.database.all(sql, params);
            return designs;
            
        } catch (error) {
            this.bot.logger.error('خطأ في البحث:', error);
            throw error;
        }
    }

    // تحديث إحصائيات التحميل
    async incrementDownloads(designId) {
        try {
            await this.bot.database.run(
                'UPDATE designs SET downloads = downloads + 1 WHERE id = ?',
                [designId]
            );
        } catch (error) {
            this.bot.logger.error('خطأ في تحديث إحصائيات التحميل:', error);
        }
    }

    // تحديث التقييم
    async updateRating(designId) {
        try {
            const result = await this.bot.database.get(
                'SELECT AVG(rating) as avg_rating, COUNT(*) as review_count FROM reviews WHERE design_id = ?',
                [designId]
            );
            
            await this.bot.database.run(
                'UPDATE designs SET rating = ?, reviews_count = ? WHERE id = ?',
                [result.avg_rating || 0, result.review_count || 0, designId]
            );
        } catch (error) {
            this.bot.logger.error('خطأ في تحديث التقييم:', error);
        }
    }
}

module.exports = DesignManager;
