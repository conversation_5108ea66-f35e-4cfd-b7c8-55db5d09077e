const { Client, GatewayIntentBits, Collection, EmbedBuilder, ActionRowBuilder, ButtonBuilder, ButtonStyle, SlashCommandBuilder } = require('discord.js');
require('dotenv').config();

class WorkingBot {
    constructor() {
        this.client = new Client({
            intents: [
                GatewayIntentBits.Guilds,
                GatewayIntentBits.GuildMessages,
                GatewayIntentBits.MessageContent
            ]
        });
        
        this.commands = new Collection();
        this.setupCommands();
        this.setupEvents();
    }

    setupCommands() {
        // أمر المتجر
        const shopCommand = {
            data: new SlashCommandBuilder()
                .setName('shop')
                .setDescription('🛍️ افتح متجر التصاميم الرئيسي'),
            async execute(interaction) {
                const embed = new EmbedBuilder()
                    .setTitle('🎨 متجر التصاميم الاحترافية')
                    .setDescription(`**مرحباً ${interaction.user.displayName}!**\n\nأهلاً بك في أكبر متجر تصاميم على ديسكورد!`)
                    .setColor('#FF6B6B')
                    .addFields(
                        { name: '🏷️ اللوجوهات', value: '500-2500 كريدت', inline: true },
                        { name: '🖼️ البانرات', value: '300-1500 كريدت', inline: true },
                        { name: '📱 السوشيال', value: '200-1000 كريدت', inline: true }
                    );

                const row1 = new ActionRowBuilder()
                    .addComponents(
                        new ButtonBuilder()
                            .setCustomId('browse_designs')
                            .setLabel('🎨 تصفح التصاميم')
                            .setStyle(ButtonStyle.Primary),
                        new ButtonBuilder()
                            .setCustomId('featured_designs')
                            .setLabel('⭐ التصاميم المميزة')
                            .setStyle(ButtonStyle.Success),
                        new ButtonBuilder()
                            .setCustomId('new_arrivals')
                            .setLabel('🆕 وصل حديثاً')
                            .setStyle(ButtonStyle.Secondary)
                    );

                const row2 = new ActionRowBuilder()
                    .addComponents(
                        new ButtonBuilder()
                            .setCustomId('my_orders')
                            .setLabel('📦 طلباتي')
                            .setStyle(ButtonStyle.Secondary),
                        new ButtonBuilder()
                            .setCustomId('custom_order')
                            .setLabel('✨ طلب مخصص')
                            .setStyle(ButtonStyle.Success),
                        new ButtonBuilder()
                            .setCustomId('support')
                            .setLabel('🎧 الدعم')
                            .setStyle(ButtonStyle.Danger)
                    );

                await interaction.reply({ embeds: [embed], components: [row1, row2] });
            }
        };

        // أمر التصاميم
        const designsCommand = {
            data: new SlashCommandBuilder()
                .setName('designs')
                .setDescription('🎨 تصفح التصاميم المتاحة'),
            async execute(interaction) {
                const embed = new EmbedBuilder()
                    .setTitle('🎨 التصاميم المتاحة')
                    .setDescription('اختر تصنيف التصاميم التي تريد تصفحها:')
                    .setColor('#4CAF50')
                    .addFields(
                        { name: '🏷️ اللوجوهات', value: 'تصاميم لوجوهات احترافية', inline: true },
                        { name: '🖼️ البانرات', value: 'بانرات لجميع المنصات', inline: true },
                        { name: '📱 السوشيال ميديا', value: 'تصاميم للشبكات الاجتماعية', inline: true }
                    );

                const row = new ActionRowBuilder()
                    .addComponents(
                        new ButtonBuilder()
                            .setCustomId('category_logos')
                            .setLabel('🏷️ اللوجوهات')
                            .setStyle(ButtonStyle.Primary),
                        new ButtonBuilder()
                            .setCustomId('category_banners')
                            .setLabel('🖼️ البانرات')
                            .setStyle(ButtonStyle.Primary),
                        new ButtonBuilder()
                            .setCustomId('category_social')
                            .setLabel('📱 السوشيال')
                            .setStyle(ButtonStyle.Primary)
                    );

                await interaction.reply({ embeds: [embed], components: [row] });
            }
        };

        // أمر الطلبات
        const ordersCommand = {
            data: new SlashCommandBuilder()
                .setName('orders')
                .setDescription('📦 عرض طلباتك ومشترياتك'),
            async execute(interaction) {
                const embed = new EmbedBuilder()
                    .setTitle('📦 طلباتي ومشترياتي')
                    .setDescription(`**مرحباً ${interaction.user.displayName}!**\n\nإليك ملخص طلباتك:`)
                    .setColor('#2196F3')
                    .addFields(
                        { name: '📊 إحصائياتك', value: '• إجمالي الطلبات: 0\n• الطلبات المكتملة: 0\n• إجمالي المنفق: 0 كريدت', inline: false }
                    );

                const row = new ActionRowBuilder()
                    .addComponents(
                        new ButtonBuilder()
                            .setCustomId('order_history')
                            .setLabel('📋 تاريخ الطلبات')
                            .setStyle(ButtonStyle.Primary),
                        new ButtonBuilder()
                            .setCustomId('download_files')
                            .setLabel('⬇️ تحميل الملفات')
                            .setStyle(ButtonStyle.Success),
                        new ButtonBuilder()
                            .setCustomId('track_order')
                            .setLabel('🔍 تتبع طلب')
                            .setStyle(ButtonStyle.Secondary)
                    );

                await interaction.reply({ embeds: [embed], components: [row] });
            }
        };

        // أمر الرصيد
        const balanceCommand = {
            data: new SlashCommandBuilder()
                .setName('balance')
                .setDescription('💰 عرض رصيدك ونقاط الولاء'),
            async execute(interaction) {
                const embed = new EmbedBuilder()
                    .setTitle('💰 محفظتي ورصيدي')
                    .setDescription(`**مرحباً ${interaction.user.displayName}!**`)
                    .setColor('#FFD700')
                    .addFields(
                        { name: '💳 الرصيد الحالي', value: '**0** كريدت', inline: true },
                        { name: '💎 نقاط الولاء', value: '**0** نقطة', inline: true },
                        { name: '🥉 العضوية', value: '**برونزي** (المستوى 1)', inline: true }
                    );

                const row = new ActionRowBuilder()
                    .addComponents(
                        new ButtonBuilder()
                            .setCustomId('add_credits')
                            .setLabel('💳 شراء كريدت')
                            .setStyle(ButtonStyle.Success),
                        new ButtonBuilder()
                            .setCustomId('redeem_points')
                            .setLabel('🎁 استبدال النقاط')
                            .setStyle(ButtonStyle.Primary),
                        new ButtonBuilder()
                            .setCustomId('transaction_history')
                            .setLabel('📋 تاريخ المعاملات')
                            .setStyle(ButtonStyle.Secondary)
                    );

                await interaction.reply({ embeds: [embed], components: [row] });
            }
        };

        // أمر المساعدة
        const helpCommand = {
            data: new SlashCommandBuilder()
                .setName('help')
                .setDescription('❓ دليل استخدام البوت والمساعدة'),
            async execute(interaction) {
                const embed = new EmbedBuilder()
                    .setTitle('❓ دليل استخدام بوت متجر التصاميم')
                    .setDescription('إليك دليل شامل لاستخدام البوت:')
                    .setColor('#2196F3')
                    .addFields(
                        { name: '🛍️ أوامر التسوق', value: '`/shop` - المتجر الرئيسي\n`/designs` - تصفح التصاميم\n`/orders` - طلباتك', inline: true },
                        { name: '💳 أوامر الدفع', value: '`/balance` - عرض الرصيد\n**الدفع:** عبر ProBot Credits', inline: true }
                    );

                const row = new ActionRowBuilder()
                    .addComponents(
                        new ButtonBuilder()
                            .setCustomId('help_shopping')
                            .setLabel('🛍️ دليل التسوق')
                            .setStyle(ButtonStyle.Primary),
                        new ButtonBuilder()
                            .setCustomId('help_payment')
                            .setLabel('💳 دليل الدفع')
                            .setStyle(ButtonStyle.Success),
                        new ButtonBuilder()
                            .setCustomId('contact_support')
                            .setLabel('🎧 تواصل مع الدعم')
                            .setStyle(ButtonStyle.Danger)
                    );

                await interaction.reply({ embeds: [embed], components: [row] });
            }
        };

        // إضافة الأوامر للمجموعة
        this.commands.set('shop', shopCommand);
        this.commands.set('designs', designsCommand);
        this.commands.set('orders', ordersCommand);
        this.commands.set('balance', balanceCommand);
        this.commands.set('help', helpCommand);
    }

    setupEvents() {
        // حدث الجاهزية
        this.client.once('ready', async () => {
            console.log(`🚀 البوت جاهز! تم تسجيل الدخول باسم ${this.client.user.tag}`);
            
            // تعيين حالة البوت
            this.client.user.setActivity('🎨 متجر التصاميم | /shop', { type: 'PLAYING' });
            
            // تسجيل الأوامر
            try {
                const commands = Array.from(this.commands.values()).map(command => command.data.toJSON());
                await this.client.application.commands.set(commands);
                console.log(`✅ تم تسجيل ${commands.length} أمر بنجاح!`);
                
                console.log('📋 الأوامر المتاحة:');
                commands.forEach((cmd, index) => {
                    console.log(`${index + 1}. /${cmd.name} - ${cmd.description}`);
                });
                
            } catch (error) {
                console.error('❌ خطأ في تسجيل الأوامر:', error);
            }
            
            console.log('🎉 البوت يعمل بنجاح! جرب الأوامر في ديسكورد');
        });

        // معالجة أوامر Slash
        this.client.on('interactionCreate', async (interaction) => {
            if (interaction.isChatInputCommand()) {
                const command = this.commands.get(interaction.commandName);
                if (!command) return;

                try {
                    await command.execute(interaction);
                } catch (error) {
                    console.error('❌ خطأ في تنفيذ الأمر:', error);
                    
                    const errorMessage = {
                        content: '❌ حدث خطأ أثناء تنفيذ الأمر!',
                        ephemeral: true
                    };
                    
                    if (interaction.replied || interaction.deferred) {
                        await interaction.followUp(errorMessage);
                    } else {
                        await interaction.reply(errorMessage);
                    }
                }
            }

            // معالجة الأزرار
            if (interaction.isButton()) {
                await this.handleButtonInteraction(interaction);
            }
        });

        // معالجة الأخطاء
        this.client.on('error', (error) => {
            console.error('❌ خطأ في البوت:', error);
        });
    }

    async handleButtonInteraction(interaction) {
        const { customId } = interaction;
        
        let embed;
        
        switch (customId) {
            case 'browse_designs':
                embed = new EmbedBuilder()
                    .setTitle('🎨 تصفح التصاميم')
                    .setDescription('هنا ستجد جميع التصاميم المتاحة مرتبة حسب التصنيف')
                    .setColor('#4CAF50');
                break;
                
            case 'featured_designs':
                embed = new EmbedBuilder()
                    .setTitle('⭐ التصاميم المميزة')
                    .setDescription('أفضل التصاميم الأعلى تقييماً في المتجر')
                    .setColor('#FFD700');
                break;
                
            case 'my_orders':
                embed = new EmbedBuilder()
                    .setTitle('📦 طلباتي')
                    .setDescription('هنا ستجد جميع طلباتك السابقة والحالية')
                    .setColor('#2196F3');
                break;
                
            case 'support':
                embed = new EmbedBuilder()
                    .setTitle('🎧 مركز الدعم')
                    .setDescription('نحن هنا لمساعدتك! تواصل معنا في أي وقت')
                    .setColor('#9C27B0');
                break;
                
            default:
                embed = new EmbedBuilder()
                    .setTitle('✅ تم الضغط على الزر')
                    .setDescription(`تم الضغط على: ${customId}`)
                    .setColor('#4CAF50');
        }
        
        await interaction.reply({ embeds: [embed], ephemeral: true });
    }

    async start() {
        try {
            if (!process.env.DISCORD_TOKEN) {
                console.error('❌ لم يتم العثور على DISCORD_TOKEN في ملف .env');
                return;
            }
            
            console.log('🔄 جاري تشغيل البوت...');
            await this.client.login(process.env.DISCORD_TOKEN);
            
        } catch (error) {
            console.error('❌ خطأ في تشغيل البوت:', error);
        }
    }
}

// تشغيل البوت
const bot = new WorkingBot();
bot.start();
