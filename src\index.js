const { Client, GatewayIntentBits, Collection, EmbedBuilder, ActionRowBuilder, ButtonBuilder, ButtonStyle } = require('discord.js');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

const Database = require('./database/Database');
const WebServer = require('./web/server');
const ProBotPayment = require('./payments/ProBotPayment');
const DesignManager = require('./utils/DesignManager');
const Logger = require('./utils/Logger');

class DesignBot {
    constructor() {
        this.client = new Client({
            intents: [
                GatewayIntentBits.Guilds,
                GatewayIntentBits.GuildMessages,
                GatewayIntentBits.MessageContent,
                GatewayIntentBits.GuildMembers,
                GatewayIntentBits.DirectMessages
            ]
        });

        this.commands = new Collection();
        this.database = new Database();
        this.webServer = new WebServer(this);
        this.proBotPayment = new ProBotPayment(this);
        this.designManager = new DesignManager(this);
        this.logger = new Logger();

        this.init();
    }

    async init() {
        try {
            // تحميل الأوامر
            await this.loadCommands();
            
            // تحميل الأحداث
            await this.loadEvents();
            
            // تهيئة قاعدة البيانات
            await this.database.init();
            
            // بدء الويب سيرفر
            await this.webServer.start();
            
            // تسجيل الدخول للبوت
            await this.client.login(process.env.DISCORD_TOKEN);
            
            this.logger.info('🚀 تم تشغيل البوت بنجاح!');
        } catch (error) {
            this.logger.error('❌ خطأ في تشغيل البوت:', error);
        }
    }

    async loadCommands() {
        const commandsPath = path.join(__dirname, 'commands');
        const commandFiles = fs.readdirSync(commandsPath).filter(file => file.endsWith('.js'));

        for (const file of commandFiles) {
            const filePath = path.join(commandsPath, file);
            const command = require(filePath);
            
            if ('data' in command && 'execute' in command) {
                this.commands.set(command.data.name, command);
                this.logger.info(`✅ تم تحميل الأمر: ${command.data.name}`);
            } else {
                this.logger.warn(`⚠️ الأمر في ${filePath} مفقود خاصية "data" أو "execute"`);
            }
        }
    }

    async loadEvents() {
        const eventsPath = path.join(__dirname, 'events');
        const eventFiles = fs.readdirSync(eventsPath).filter(file => file.endsWith('.js'));

        for (const file of eventFiles) {
            const filePath = path.join(eventsPath, file);
            const event = require(filePath);
            
            if (event.once) {
                this.client.once(event.name, (...args) => event.execute(...args, this));
            } else {
                this.client.on(event.name, (...args) => event.execute(...args, this));
            }
            
            this.logger.info(`✅ تم تحميل الحدث: ${event.name}`);
        }
    }

    // إنشاء بانل رئيسي
    createMainPanel() {
        const embed = new EmbedBuilder()
            .setTitle('🎨 متجر التصاميم الاحترافية')
            .setDescription('مرحباً بك في متجر التصاميم الأكثر تطوراً على ديسكورد!')
            .setColor('#FF6B6B')
            .addFields(
                { name: '🛍️ التصاميم المتاحة', value: 'تصفح مجموعتنا الواسعة من التصاميم', inline: true },
                { name: '💰 الأسعار', value: 'أسعار تنافسية وعروض خاصة', inline: true },
                { name: '🎯 مخصص', value: 'اطلب تصميم حسب احتياجاتك', inline: true }
            )
            .setThumbnail('https://cdn.discordapp.com/attachments/placeholder.png')
            .setFooter({ text: 'متجر التصاميم • جودة عالية • خدمة سريعة' })
            .setTimestamp();

        const row = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('browse_designs')
                    .setLabel('🎨 تصفح التصاميم')
                    .setStyle(ButtonStyle.Primary),
                new ButtonBuilder()
                    .setCustomId('my_orders')
                    .setLabel('📦 طلباتي')
                    .setStyle(ButtonStyle.Secondary),
                new ButtonBuilder()
                    .setCustomId('custom_order')
                    .setLabel('✨ طلب مخصص')
                    .setStyle(ButtonStyle.Success),
                new ButtonBuilder()
                    .setCustomId('support')
                    .setLabel('🎧 الدعم')
                    .setStyle(ButtonStyle.Danger)
            );

        return { embeds: [embed], components: [row] };
    }
}

// تشغيل البوت
new DesignBot();
