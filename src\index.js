const { Client, GatewayIntentBits, Collection, EmbedBuilder, ActionRowBuilder, ButtonBuilder, ButtonStyle } = require('discord.js');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

// تعطيل المكونات المعقدة مؤقتاً للتركيز على الأوامر
// const Database = require('./database/Database');
// const WebServer = require('./web/server');
// const ProBotPayment = require('./payments/ProBotPayment');
// const DesignManager = require('./utils/DesignManager');
// const Logger = require('./utils/Logger');
// const SampleData = require('./utils/SampleData');

class DesignBot {
    constructor() {
        this.client = new Client({
            intents: [
                GatewayIntentBits.Guilds,
                GatewayIntentBits.GuildMessages,
                GatewayIntentBits.MessageContent,
                GatewayIntentBits.GuildMembers,
                GatewayIntentBits.DirectMessages
            ]
        });

        this.commands = new Collection();
        this.database = new Database();
        this.webServer = new WebServer(this);
        this.proBotPayment = new ProBotPayment(this);
        this.designManager = new DesignManager(this);
        this.logger = new Logger();
        this.sampleData = new SampleData(this);

        this.init();
    }

    async init() {
        try {
            // تحميل الأوامر
            await this.loadCommands();
            
            // تحميل الأحداث
            await this.loadEvents();
            
            // تهيئة قاعدة البيانات
            await this.database.init();

            // إعداد البيانات التجريبية (في المرة الأولى فقط)
            if (process.env.NODE_ENV === 'development') {
                await this.setupSampleDataIfNeeded();
            }
            
            // بدء الويب سيرفر
            await this.webServer.start();

            // بدء مراقبة الدفعات
            // this.proBotPayment.startPaymentMonitoring(); // سيتم تفعيلها في ready event
            
            // تسجيل الدخول للبوت
            await this.client.login(process.env.DISCORD_TOKEN);
            
            this.logger.info('🚀 تم تشغيل البوت بنجاح!');
        } catch (error) {
            this.logger.error('❌ خطأ في تشغيل البوت:', error);
        }
    }

    async loadCommands() {
        const commandsPath = path.join(__dirname, 'commands');
        const commandFiles = fs.readdirSync(commandsPath).filter(file => file.endsWith('.js'));

        for (const file of commandFiles) {
            const filePath = path.join(commandsPath, file);
            const command = require(filePath);
            
            if ('data' in command && 'execute' in command) {
                this.commands.set(command.data.name, command);
                this.logger.info(`✅ تم تحميل الأمر: ${command.data.name}`);
            } else {
                this.logger.warn(`⚠️ الأمر في ${filePath} مفقود خاصية "data" أو "execute"`);
            }
        }
    }

    async loadEvents() {
        const eventsPath = path.join(__dirname, 'events');
        const eventFiles = fs.readdirSync(eventsPath).filter(file => file.endsWith('.js'));

        for (const file of eventFiles) {
            const filePath = path.join(eventsPath, file);
            const event = require(filePath);
            
            if (event.once) {
                this.client.once(event.name, (...args) => event.execute(...args, this));
            } else {
                this.client.on(event.name, (...args) => event.execute(...args, this));
            }
            
            this.logger.info(`✅ تم تحميل الحدث: ${event.name}`);
        }
    }

    // إنشاء بانل رئيسي
    createMainPanel() {
        const embed = new EmbedBuilder()
            .setTitle('🎨 متجر التصاميم الاحترافية')
            .setDescription('مرحباً بك في متجر التصاميم الأكثر تطوراً على ديسكورد!')
            .setColor('#FF6B6B')
            .addFields(
                { name: '🛍️ التصاميم المتاحة', value: 'تصفح مجموعتنا الواسعة من التصاميم', inline: true },
                { name: '💰 الأسعار', value: 'أسعار تنافسية وعروض خاصة', inline: true },
                { name: '🎯 مخصص', value: 'اطلب تصميم حسب احتياجاتك', inline: true }
            )
            .setThumbnail('https://cdn.discordapp.com/attachments/placeholder.png')
            .setFooter({ text: 'متجر التصاميم • جودة عالية • خدمة سريعة' })
            .setTimestamp();

        const row = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('browse_designs')
                    .setLabel('🎨 تصفح التصاميم')
                    .setStyle(ButtonStyle.Primary),
                new ButtonBuilder()
                    .setCustomId('my_orders')
                    .setLabel('📦 طلباتي')
                    .setStyle(ButtonStyle.Secondary),
                new ButtonBuilder()
                    .setCustomId('custom_order')
                    .setLabel('✨ طلب مخصص')
                    .setStyle(ButtonStyle.Success),
                new ButtonBuilder()
                    .setCustomId('support')
                    .setLabel('🎧 الدعم')
                    .setStyle(ButtonStyle.Danger)
            );

        return { embeds: [embed], components: [row] };
    }

    // بدء المهام المجدولة
    startScheduledTasks() {
        const cron = require('node-cron');

        // تنظيف الدفعات المنتهية الصلاحية كل 10 دقائق
        cron.schedule('*/10 * * * *', () => {
            this.proBotPayment.cleanupExpiredPayments();
            this.logger.info('تم تنظيف الدفعات المنتهية الصلاحية');
        });

        // تنظيف السجلات القديمة يومياً في الساعة 2 صباحاً
        cron.schedule('0 2 * * *', () => {
            this.logger.cleanOldLogs(30);
            this.logger.info('تم تنظيف السجلات القديمة');
        });

        // إرسال تقرير يومي للإدارة
        cron.schedule('0 9 * * *', async () => {
            await this.sendDailyReport();
        });

        this.logger.info('✅ تم بدء المهام المجدولة');
    }

    // إرسال تقرير يومي
    async sendDailyReport() {
        try {
            if (!process.env.ADMIN_USER_ID) return;

            const admin = await this.client.users.fetch(process.env.ADMIN_USER_ID);
            const today = new Date().toISOString().split('T')[0];

            // جلب إحصائيات اليوم
            const stats = await this.database.all(`
                SELECT
                    COUNT(*) as orders_today,
                    SUM(total_amount) as revenue_today
                FROM orders
                WHERE DATE(created_at) = ? AND status = 'completed'
            `, [today]);

            const newUsers = await this.database.get(`
                SELECT COUNT(*) as count
                FROM users
                WHERE DATE(created_at) = ?
            `, [today]);

            const embed = new EmbedBuilder()
                .setTitle('📊 التقرير اليومي')
                .setDescription(`تقرير أداء المتجر ليوم ${today}`)
                .setColor('#4CAF50')
                .addFields(
                    { name: '📦 الطلبات', value: `${stats[0]?.orders_today || 0} طلب`, inline: true },
                    { name: '💰 الإيرادات', value: `${stats[0]?.revenue_today || 0} كريدت`, inline: true },
                    { name: '👥 مستخدمين جدد', value: `${newUsers.count || 0} مستخدم`, inline: true }
                )
                .setTimestamp();

            await admin.send({ embeds: [embed] });

        } catch (error) {
            this.logger.error('خطأ في إرسال التقرير اليومي:', error);
        }
    }

    // إعداد البيانات التجريبية إذا لم تكن موجودة
    async setupSampleDataIfNeeded() {
        try {
            const designCount = await this.database.get('SELECT COUNT(*) as count FROM designs');

            if (designCount.count === 0) {
                this.logger.info('🔄 إعداد البيانات التجريبية...');
                await this.sampleData.setupSampleData();
                this.logger.success('✅ تم إعداد البيانات التجريبية بنجاح');
            }
        } catch (error) {
            this.logger.error('خطأ في إعداد البيانات التجريبية:', error);
        }
    }
}

// تشغيل البوت
new DesignBot();
