const { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ActionRowBuilder, ButtonBuilder, ButtonStyle } = require('discord.js');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('balance')
        .setDescription('💰 عرض رصيدك ونقاط الولاء'),
    
    async execute(interaction, bot) {
        try {
            // جلب معلومات المستخدم
            let user = await bot.database.get(
                'SELECT * FROM users WHERE discord_id = ?',
                [interaction.user.id]
            );
            
            // إنشاء المستخدم إذا لم يكن موجوداً
            if (!user) {
                await bot.database.run(
                    'INSERT INTO users (discord_id, username) VALUES (?, ?)',
                    [interaction.user.id, interaction.user.username]
                );
                
                user = {
                    discord_id: interaction.user.id,
                    username: interaction.user.username,
                    balance: 0,
                    total_spent: 0,
                    loyalty_points: 0,
                    created_at: new Date().toISOString()
                };
            }
            
            // جلب إحصائيات الطلبات
            const orderStats = await bot.database.get(`
                SELECT 
                    COUNT(*) as total_orders,
                    COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_orders,
                    COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_orders,
                    SUM(CASE WHEN status = 'completed' THEN total_amount ELSE 0 END) as total_spent
                FROM orders 
                WHERE user_id = (SELECT id FROM users WHERE discord_id = ?)
            `, [interaction.user.id]);
            
            // حساب المستوى بناءً على نقاط الولاء
            const level = Math.floor((user.loyalty_points || 0) / 100) + 1;
            const nextLevelPoints = level * 100;
            const currentLevelProgress = (user.loyalty_points || 0) % 100;
            
            // تحديد رتبة العضوية
            let membershipTier = 'برونزي';
            let tierEmoji = '🥉';
            let tierColor = '#CD7F32';
            
            if (level >= 10) {
                membershipTier = 'ماسي';
                tierEmoji = '💎';
                tierColor = '#B9F2FF';
            } else if (level >= 5) {
                membershipTier = 'ذهبي';
                tierEmoji = '🥇';
                tierColor = '#FFD700';
            } else if (level >= 3) {
                membershipTier = 'فضي';
                tierEmoji = '🥈';
                tierColor = '#C0C0C0';
            }
            
            const embed = new EmbedBuilder()
                .setTitle('💰 محفظتي ورصيدي')
                .setDescription(`
**مرحباً ${interaction.user.displayName}!**

إليك ملخص رصيدك ونشاطك في المتجر:
                `)
                .setColor(tierColor)
                .setThumbnail(interaction.user.displayAvatarURL())
                .addFields(
                    {
                        name: '💳 الرصيد الحالي',
                        value: `**${user.balance || 0}** كريدت`,
                        inline: true
                    },
                    {
                        name: '💎 نقاط الولاء',
                        value: `**${user.loyalty_points || 0}** نقطة`,
                        inline: true
                    },
                    {
                        name: `${tierEmoji} العضوية`,
                        value: `**${membershipTier}** (المستوى ${level})`,
                        inline: true
                    },
                    {
                        name: '📊 إحصائيات الطلبات',
                        value: `
• **إجمالي الطلبات:** ${orderStats.total_orders || 0}
• **طلبات مكتملة:** ${orderStats.completed_orders || 0}
• **طلبات معلقة:** ${orderStats.pending_orders || 0}
                        `,
                        inline: true
                    },
                    {
                        name: '💸 إجمالي المصروفات',
                        value: `**${orderStats.total_spent || 0}** كريدت`,
                        inline: true
                    },
                    {
                        name: '📈 التقدم للمستوى التالي',
                        value: `${currentLevelProgress}/${nextLevelPoints - (level-1)*100} نقطة\n${'▓'.repeat(Math.floor(currentLevelProgress/10))}${'░'.repeat(10-Math.floor(currentLevelProgress/10))} ${Math.floor(currentLevelProgress/10*10)}%`,
                        inline: false
                    }
                )
                .setFooter({ 
                    text: `عضو منذ ${new Date(user.created_at).toLocaleDateString('ar-EG')}` 
                })
                .setTimestamp();
            
            // مميزات العضوية
            let membershipBenefits = '';
            switch (membershipTier) {
                case 'ماسي':
                    membershipBenefits = '• خصم 25% على جميع التصاميم\n• أولوية في الدعم\n• تصاميم حصرية\n• تسليم فوري';
                    break;
                case 'ذهبي':
                    membershipBenefits = '• خصم 15% على جميع التصاميم\n• أولوية في الدعم\n• وصول مبكر للتصاميم الجديدة';
                    break;
                case 'فضي':
                    membershipBenefits = '• خصم 10% على جميع التصاميم\n• دعم أولوية';
                    break;
                default:
                    membershipBenefits = '• خصم 5% على التصاميم\n• دعم عادي';
            }
            
            embed.addFields({
                name: '🎁 مميزات عضويتك',
                value: membershipBenefits,
                inline: false
            });
            
            // أزرار التحكم
            const row1 = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId('add_credits')
                        .setLabel('💳 شراء كريدت')
                        .setStyle(ButtonStyle.Success),
                    new ButtonBuilder()
                        .setCustomId('redeem_points')
                        .setLabel('🎁 استبدال النقاط')
                        .setStyle(ButtonStyle.Primary),
                    new ButtonBuilder()
                        .setCustomId('transaction_history')
                        .setLabel('📋 تاريخ المعاملات')
                        .setStyle(ButtonStyle.Secondary)
                );
            
            const row2 = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId('referral_program')
                        .setLabel('👥 برنامج الإحالة')
                        .setStyle(ButtonStyle.Secondary),
                    new ButtonBuilder()
                        .setCustomId('loyalty_rewards')
                        .setLabel('🏆 مكافآت الولاء')
                        .setStyle(ButtonStyle.Primary),
                    new ButtonBuilder()
                        .setCustomId('membership_upgrade')
                        .setLabel('⬆️ ترقية العضوية')
                        .setStyle(ButtonStyle.Success)
                );
            
            await interaction.reply({ 
                embeds: [embed], 
                components: [row1, row2] 
            });
            
        } catch (error) {
            console.error('خطأ في عرض الرصيد:', error);
            
            const errorEmbed = new EmbedBuilder()
                .setTitle('❌ حدث خطأ')
                .setDescription('عذراً، حدث خطأ أثناء جلب معلومات رصيدك. يرجى المحاولة مرة أخرى.')
                .setColor('#FF0000');
            
            await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
        }
    }
};
